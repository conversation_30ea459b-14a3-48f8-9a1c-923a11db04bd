"""
应用程序入口模块

应用程序的主入口点。
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.utils import Logger
from infrastructure.config import initialize_config
from presentation.main_window import MainWindow


class QuantTradingApp:
    """
    量化交易应用程序
    
    应用程序的主类，负责初始化和启动整个系统。
    """
    
    def __init__(self):
        self.logger = None
        self.config = None
        self.main_window = None
    
    def initialize(self):
        """初始化应用程序"""
        try:
            # 设置日志
            self._setup_logging()
            
            # 初始化配置
            self._setup_config()
            
            # 创建主窗口
            self._setup_main_window()
            
            self.logger.info("应用程序初始化完成")
            
        except Exception as e:
            print(f"应用程序初始化失败: {e}")
            sys.exit(1)
    
    def _setup_logging(self):
        """设置日志"""
        # 确保日志目录存在
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 配置根日志记录器
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "app.log", encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = Logger.get_logger("app")
        self.logger.info("日志系统已初始化")
    
    def _setup_config(self):
        """设置配置"""
        try:
            # 确保配置目录存在
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)
            
            # 创建默认配置文件（如果不存在）
            self._create_default_configs(config_dir)
            
            # 初始化配置管理器
            self.config = initialize_config(config_dir)
            
            self.logger.info("配置系统已初始化")
            
        except Exception as e:
            self.logger.error(f"配置初始化失败: {e}")
            raise
    
    def _create_default_configs(self, config_dir: Path):
        """创建默认配置文件"""
        # 系统配置
        system_config_file = config_dir / "system.yaml"
        if not system_config_file.exists():
            system_config = """
# 系统配置
system:
  logging:
    level: INFO
    file: logs/app.log
  
  performance:
    cache_size: 1000
    max_workers: 4
  
  cache:
    ttl: 3600
    max_size: 1000
  
  notifications:
    enabled: true
    channels: ["console"]
    email:
      smtp_server: ""
      smtp_port: 587
      use_tls: true

# 策略执行配置
strategies:
  execution:
    risk_control:
      max_position_size: 0.25
      max_total_exposure: 0.8
      max_drawdown: 0.2
      max_daily_loss: 0.05
      max_var_ratio: 0.1
      max_correlation: 0.7
      max_positions: 10
      stop_loss_threshold: 0.02
      take_profit_ratio: 2.0
    
    signal_filtering:
      enabled: true
      min_signal_strength: 0.5
      max_signal_age_seconds: 300
      dedup_window_seconds: 60
"""
            with open(system_config_file, 'w', encoding='utf-8') as f:
                f.write(system_config)
        
        # 数据源配置
        data_sources_config_file = config_dir / "data_sources.yaml"
        if not data_sources_config_file.exists():
            data_sources_config = """
# 数据源配置
data_sources:
  binance:
    enabled: false
    api_key: ""
    secret_key: ""
    use_testnet: true
    rate_limit: 1200
  
  yahoo_finance:
    enabled: true
    rate_limit: 100
  
  local_file:
    enabled: true
    data_directory: "data/market_data"
"""
            with open(data_sources_config_file, 'w', encoding='utf-8') as f:
                f.write(data_sources_config)
        
        # 策略配置
        strategies_config_file = config_dir / "strategies.yaml"
        if not strategies_config_file.exists():
            strategies_config = """
# 策略配置
strategies:
  default:
    sma_cross:
      enabled: false
      fast_period: 10
      slow_period: 30
      signal_threshold: 0.01
    
    rsi:
      enabled: false
      period: 14
      oversold_threshold: 30
      overbought_threshold: 70
"""
            with open(strategies_config_file, 'w', encoding='utf-8') as f:
                f.write(strategies_config)
    
    def _setup_main_window(self):
        """设置主窗口"""
        try:
            self.main_window = MainWindow(self.config)
            self.logger.info("主窗口已创建")
            
        except Exception as e:
            self.logger.error(f"主窗口创建失败: {e}")
            raise
    
    def run(self):
        """运行应用程序"""
        try:
            self.logger.info("启动量化交易系统")
            
            # 运行主窗口
            self.main_window.run()
            
        except KeyboardInterrupt:
            self.logger.info("用户中断，正在退出...")
        except Exception as e:
            self.logger.error(f"应用程序运行失败: {e}")
        finally:
            self.logger.info("应用程序已退出")


def main():
    """主函数"""
    print("正在启动量化交易系统...")
    
    # 创建应用程序实例
    app = QuantTradingApp()
    
    # 初始化应用程序
    app.initialize()
    
    # 运行应用程序
    app.run()


if __name__ == "__main__":
    main()
