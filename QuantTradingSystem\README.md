# 量化交易系统

一个基于Python 3.13开发的现代化量化交易系统，采用领域驱动设计(DDD)和清洁架构原则。

## 特性

### 核心功能
- **策略引擎**: 支持多种交易策略，包括移动平均交叉、RSI等
- **回测引擎**: 完整的历史数据回测功能
- **参数优化**: 支持网格搜索、随机搜索、贝叶斯优化
- **风险管理**: 实时风险控制和监控
- **数据管理**: 统一的数据获取和管理接口
- **实时交易**: 支持模拟交易和实盘交易

### 技术特性
- **Python 3.13**: 利用最新Python特性，包括自由线程模式
- **异步架构**: 高性能的异步处理能力
- **模块化设计**: 清晰的分层架构，易于扩展和维护
- **配置管理**: 灵活的配置系统，支持热加载
- **日志系统**: 完整的日志记录和监控
- **GUI界面**: 直观的图形用户界面

## 系统架构

```
QuantTradingSystem/
├── domain/              # 领域层 - 核心业务逻辑
│   ├── entities/        # 实体
│   ├── value_objects/   # 值对象
│   ├── services/        # 领域服务
│   └── repositories/    # 仓储接口
├── application/         # 应用层 - 业务流程编排
│   ├── strategies/      # 策略实现
│   ├── services/        # 应用服务
│   └── orchestrators/   # 业务协调器
├── infrastructure/      # 基础设施层 - 外部依赖
│   ├── data_sources/    # 数据源
│   ├── config/          # 配置管理
│   └── external/        # 外部服务
├── presentation/        # 展示层 - 用户界面
│   ├── gui/            # 图形界面
│   ├── controllers/    # 控制器
│   └── app.py          # 应用入口
└── shared/             # 共享组件
    ├── constants/      # 常量定义
    ├── exceptions/     # 异常类
    └── utils/          # 工具函数
```

## 快速开始

### 环境要求

- Python 3.13+ (推荐)
- 操作系统: Windows/Linux/macOS

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行系统

#### 图形界面模式
```bash
python run.py
```

#### 命令行模式
```bash
python main.py --mode gui
```

#### 回测模式
```bash
python main.py --mode backtest --strategy sma_cross --symbol AAPL
```

#### 参数优化模式
```bash
python main.py --mode optimize --strategy sma_cross --symbol AAPL
```

## 配置

系统使用YAML格式的配置文件，主要配置文件包括：

- `config/system.yaml` - 系统配置
- `config/data_sources.yaml` - 数据源配置
- `config/strategies.yaml` - 策略配置

### 示例配置

```yaml
# system.yaml
system:
  logging:
    level: INFO
    file: logs/app.log
  
  performance:
    cache_size: 1000
    max_workers: 4

# data_sources.yaml
data_sources:
  yahoo_finance:
    enabled: true
    rate_limit: 100
  
  binance:
    enabled: false
    api_key: ""
    secret_key: ""

# strategies.yaml
strategies:
  default:
    sma_cross:
      enabled: true
      fast_period: 10
      slow_period: 30
      signal_threshold: 0.01
```

## 策略开发

### 创建自定义策略

```python
from application.strategies import IStrategy, StrategyContext
from domain.value_objects import MarketData, Signal

class MyCustomStrategy(IStrategy):
    def __init__(self, strategy_id: str, config: Dict[str, Any]):
        super().__init__(strategy_id, config)
        # 初始化策略参数
    
    async def initialize(self, context: StrategyContext) -> None:
        # 策略初始化逻辑
        pass
    
    async def on_data(self, data: MarketData) -> List[Signal]:
        # 数据处理和信号生成逻辑
        signals = []
        # ... 策略逻辑 ...
        return signals
    
    def get_parameters(self) -> Dict[str, Any]:
        # 返回策略参数
        return {}
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        # 验证策略参数
        return True
```

### 注册策略

```python
from application.strategies import STRATEGY_REGISTRY

# 注册策略
STRATEGY_REGISTRY['my_strategy'] = MyCustomStrategy
```

## 回测示例

```python
import asyncio
from datetime import datetime, timedelta
from application.services import BacktestService
from infrastructure.config import ConfigurationManager

async def run_backtest():
    config = ConfigurationManager()
    backtest_service = BacktestService(config)
    
    result = await backtest_service.run_backtest(
        strategy_name='sma_cross',
        symbol='AAPL',
        start_date=datetime.now() - timedelta(days=365),
        end_date=datetime.now(),
        initial_capital=100000.0
    )
    
    print(f"总收益率: {result.performance.total_return:.2%}")
    print(f"夏普比率: {result.performance.sharpe_ratio:.3f}")
    print(f"最大回撤: {result.performance.max_drawdown:.2%}")

# 运行回测
asyncio.run(run_backtest())
```

## 数据源

系统支持多种数据源：

- **Yahoo Finance**: 免费的股票数据
- **Binance**: 加密货币数据
- **本地文件**: CSV格式的历史数据

### 添加自定义数据源

```python
from infrastructure.data_sources import IDataSource

class MyDataSource(IDataSource):
    async def get_historical_data(self, symbol, timeframe, start_date, end_date):
        # 实现数据获取逻辑
        pass
    
    async def get_realtime_data(self, symbol):
        # 实现实时数据获取逻辑
        pass
```

## 风险管理

系统内置多种风险控制机制：

- **仓位大小控制**: 限制单个仓位的最大比例
- **总敞口控制**: 限制总的市场敞口
- **回撤控制**: 监控和限制最大回撤
- **日损失控制**: 限制单日最大损失
- **相关性控制**: 避免高度相关的持仓

## 测试

运行测试套件：

```bash
python -m pytest tests/
```

运行特定测试：

```bash
python -m pytest tests/test_strategies.py
```

## 性能优化

### Python 3.13 自由线程模式

系统支持Python 3.13的自由线程模式，可以显著提升并发性能：

```bash
python -X gil=0 main.py
```

### 配置优化

- 调整缓存大小: `system.performance.cache_size`
- 设置工作线程数: `system.performance.max_workers`
- 优化数据源速率限制: `data_sources.*.rate_limit`

## 监控和日志

系统提供完整的监控和日志功能：

- **实时监控**: 系统状态、策略性能、风险指标
- **日志记录**: 分级日志、文件轮转、结构化日志
- **性能指标**: 执行时间、内存使用、缓存命中率

## 部署

### 开发环境

```bash
git clone <repository>
cd QuantTradingSystem
pip install -r requirements.txt
python run.py
```

### 生产环境

1. 配置生产环境参数
2. 设置环境变量
3. 启动系统服务
4. 配置监控和告警

## 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱: [<EMAIL>]
- 项目主页: [project-url]

## 更新日志

### v1.0.0
- 初始版本发布
- 基础策略引擎
- 回测功能
- GUI界面

---

**注意**: 本系统仅供学习和研究使用，实盘交易请谨慎评估风险。
