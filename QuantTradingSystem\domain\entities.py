"""
实体类模块

定义系统中的实体对象，这些对象有唯一标识符，可以被修改。
实体封装了复杂的业务逻辑和状态管理。
"""

from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional, Set
from uuid import UUID, uuid4

from .value_objects import Price, Volume, Order, PerformanceMetrics, MarketData
from shared.constants import (
    PositionSide, PositionStatus, StrategyStatus, AssetType
)
from shared.exceptions import (
    PortfolioException, InsufficientFundsException, 
    InvalidPositionException, StrategyException
)


@dataclass
class Position:
    """持仓实体"""
    id: UUID = field(default_factory=uuid4)
    symbol: str = ""
    side: PositionSide = PositionSide.LONG
    quantity: Volume = field(default_factory=lambda: Volume(Decimal('0')))
    average_price: Price = field(default_factory=lambda: Price(Decimal('0')))
    current_price: Price = field(default_factory=lambda: Price(Decimal('0')))
    status: PositionStatus = PositionStatus.OPEN
    opened_at: datetime = field(default_factory=datetime.utcnow)
    closed_at: Optional[datetime] = None
    strategy_id: Optional[str] = None
    stop_loss: Optional[Price] = None
    take_profit: Optional[Price] = None
    commission_paid: Price = field(default_factory=lambda: Price(Decimal('0')))
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.symbol:
            raise InvalidPositionException("交易品种不能为空")
        
        if self.quantity.value <= 0:
            raise InvalidPositionException("持仓数量必须大于0")
        
        if self.average_price.value <= 0:
            raise InvalidPositionException("平均价格必须大于0")
    
    @property
    def market_value(self) -> Price:
        """市场价值"""
        return self.current_price * float(self.quantity.value)
    
    @property
    def cost_basis(self) -> Price:
        """成本基础"""
        return self.average_price * float(self.quantity.value)
    
    @property
    def unrealized_pnl(self) -> Price:
        """未实现盈亏"""
        if self.side == PositionSide.LONG:
            return self.market_value - self.cost_basis
        else:  # SHORT
            return self.cost_basis - self.market_value
    
    @property
    def unrealized_pnl_percent(self) -> float:
        """未实现盈亏百分比"""
        if self.cost_basis.value == 0:
            return 0.0
        return float(self.unrealized_pnl.value / self.cost_basis.value)
    
    @property
    def is_profitable(self) -> bool:
        """是否盈利"""
        return self.unrealized_pnl.value > 0
    
    @property
    def is_open(self) -> bool:
        """是否开仓状态"""
        return self.status == PositionStatus.OPEN
    
    def update_price(self, new_price: Price) -> None:
        """更新当前价格"""
        if new_price.currency != self.current_price.currency:
            raise InvalidPositionException("价格货币不匹配")
        self.current_price = new_price
    
    def should_stop_loss(self) -> bool:
        """是否应该止损"""
        if not self.stop_loss or not self.is_open:
            return False
        
        if self.side == PositionSide.LONG:
            return self.current_price.value <= self.stop_loss.value
        else:  # SHORT
            return self.current_price.value >= self.stop_loss.value
    
    def should_take_profit(self) -> bool:
        """是否应该止盈"""
        if not self.take_profit or not self.is_open:
            return False
        
        if self.side == PositionSide.LONG:
            return self.current_price.value >= self.take_profit.value
        else:  # SHORT
            return self.current_price.value <= self.take_profit.value
    
    def close(self, close_price: Price, close_time: Optional[datetime] = None) -> None:
        """平仓"""
        if not self.is_open:
            raise InvalidPositionException("仓位已关闭")
        
        self.current_price = close_price
        self.status = PositionStatus.CLOSED
        self.closed_at = close_time or datetime.utcnow()
    
    def partial_close(self, quantity: Volume, close_price: Price) -> 'Position':
        """部分平仓，返回新的已平仓位"""
        if not self.is_open:
            raise InvalidPositionException("仓位已关闭")
        
        if quantity.value >= self.quantity.value:
            raise InvalidPositionException("平仓数量不能大于等于持仓数量")
        
        # 减少当前持仓数量
        self.quantity = Volume(self.quantity.value - quantity.value)
        
        # 创建已平仓位记录
        closed_position = Position(
            symbol=self.symbol,
            side=self.side,
            quantity=quantity,
            average_price=self.average_price,
            current_price=close_price,
            status=PositionStatus.CLOSED,
            opened_at=self.opened_at,
            closed_at=datetime.utcnow(),
            strategy_id=self.strategy_id
        )
        
        return closed_position


@dataclass
class Transaction:
    """交易记录实体"""
    id: UUID = field(default_factory=uuid4)
    symbol: str = ""
    side: PositionSide = PositionSide.LONG
    quantity: Volume = field(default_factory=lambda: Volume(Decimal('0')))
    price: Price = field(default_factory=lambda: Price(Decimal('0')))
    commission: Price = field(default_factory=lambda: Price(Decimal('0')))
    timestamp: datetime = field(default_factory=datetime.utcnow)
    order_id: Optional[UUID] = None
    position_id: Optional[UUID] = None
    strategy_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def gross_amount(self) -> Price:
        """总金额（不含手续费）"""
        return self.price * float(self.quantity.value)
    
    @property
    def net_amount(self) -> Price:
        """净金额（含手续费）"""
        if self.side == PositionSide.LONG:
            return self.gross_amount + self.commission
        else:
            return self.gross_amount - self.commission


@dataclass
class Account:
    """账户实体"""
    id: UUID = field(default_factory=uuid4)
    name: str = ""
    initial_balance: Price = field(default_factory=lambda: Price(Decimal('0')))
    current_balance: Price = field(default_factory=lambda: Price(Decimal('0')))
    available_balance: Price = field(default_factory=lambda: Price(Decimal('0')))
    frozen_balance: Price = field(default_factory=lambda: Price(Decimal('0')))
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.name:
            raise PortfolioException("账户名称不能为空")
        
        if self.initial_balance.value < 0:
            raise PortfolioException("初始余额不能为负数")
        
        # 如果当前余额未设置，使用初始余额
        if self.current_balance.value == 0:
            self.current_balance = self.initial_balance
            self.available_balance = self.initial_balance
    
    @property
    def total_balance(self) -> Price:
        """总余额"""
        return Price(
            self.available_balance.value + self.frozen_balance.value,
            self.available_balance.currency
        )
    
    def can_withdraw(self, amount: Price) -> bool:
        """是否可以提取指定金额"""
        return (
            self.is_active and 
            amount.currency == self.available_balance.currency and
            amount.value <= self.available_balance.value
        )
    
    def withdraw(self, amount: Price, description: str = "") -> None:
        """提取资金"""
        if not self.can_withdraw(amount):
            raise InsufficientFundsException(
                f"余额不足，可用余额: {self.available_balance}, 提取金额: {amount}"
            )
        
        self.available_balance = Price(
            self.available_balance.value - amount.value,
            self.available_balance.currency
        )
        self.current_balance = Price(
            self.current_balance.value - amount.value,
            self.current_balance.currency
        )
        self.updated_at = datetime.utcnow()
    
    def deposit(self, amount: Price, description: str = "") -> None:
        """存入资金"""
        if amount.currency != self.current_balance.currency:
            raise PortfolioException("存入资金货币与账户货币不匹配")
        
        if amount.value <= 0:
            raise PortfolioException("存入金额必须大于0")
        
        self.available_balance = Price(
            self.available_balance.value + amount.value,
            self.available_balance.currency
        )
        self.current_balance = Price(
            self.current_balance.value + amount.value,
            self.current_balance.currency
        )
        self.updated_at = datetime.utcnow()
    
    def freeze(self, amount: Price) -> None:
        """冻结资金"""
        if not self.can_withdraw(amount):
            raise InsufficientFundsException("可用余额不足，无法冻结")
        
        self.available_balance = Price(
            self.available_balance.value - amount.value,
            self.available_balance.currency
        )
        self.frozen_balance = Price(
            self.frozen_balance.value + amount.value,
            self.frozen_balance.currency
        )
        self.updated_at = datetime.utcnow()
    
    def unfreeze(self, amount: Price) -> None:
        """解冻资金"""
        if amount.value > self.frozen_balance.value:
            raise PortfolioException("解冻金额超过冻结余额")
        
        self.frozen_balance = Price(
            self.frozen_balance.value - amount.value,
            self.frozen_balance.currency
        )
        self.available_balance = Price(
            self.available_balance.value + amount.value,
            self.available_balance.currency
        )
        self.updated_at = datetime.utcnow()


@dataclass
class Portfolio:
    """投资组合实体"""
    id: UUID = field(default_factory=uuid4)
    name: str = ""
    account: Account = field(default_factory=Account)
    positions: Dict[str, Position] = field(default_factory=dict)
    transactions: List[Transaction] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.name:
            raise PortfolioException("投资组合名称不能为空")
    
    @property
    def total_market_value(self) -> Price:
        """总市值"""
        if not self.positions:
            return Price(Decimal('0'), self.account.current_balance.currency)
        
        total = Decimal('0')
        currency = self.account.current_balance.currency
        
        for position in self.positions.values():
            if position.is_open:
                total += position.market_value.value
        
        return Price(total, currency)
    
    @property
    def total_cost_basis(self) -> Price:
        """总成本基础"""
        if not self.positions:
            return Price(Decimal('0'), self.account.current_balance.currency)
        
        total = Decimal('0')
        currency = self.account.current_balance.currency
        
        for position in self.positions.values():
            if position.is_open:
                total += position.cost_basis.value
        
        return Price(total, currency)
    
    @property
    def total_unrealized_pnl(self) -> Price:
        """总未实现盈亏"""
        return self.total_market_value - self.total_cost_basis
    
    @property
    def total_value(self) -> Price:
        """总价值（现金 + 持仓市值）"""
        return Price(
            self.account.current_balance.value + self.total_market_value.value,
            self.account.current_balance.currency
        )
    
    @property
    def cash_ratio(self) -> float:
        """现金比例"""
        if self.total_value.value == 0:
            return 1.0
        return float(self.account.current_balance.value / self.total_value.value)
    
    def add_position(self, position: Position) -> None:
        """添加持仓"""
        if position.symbol in self.positions:
            existing = self.positions[position.symbol]
            if existing.is_open:
                raise PortfolioException(f"已存在开仓的{position.symbol}持仓")
        
        self.positions[position.symbol] = position
        self.updated_at = datetime.utcnow()
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取持仓"""
        return self.positions.get(symbol)
    
    def close_position(self, symbol: str, close_price: Price) -> None:
        """平仓"""
        position = self.get_position(symbol)
        if not position or not position.is_open:
            raise InvalidPositionException(f"没有找到{symbol}的开仓持仓")
        
        position.close(close_price)
        self.updated_at = datetime.utcnow()
    
    def add_transaction(self, transaction: Transaction) -> None:
        """添加交易记录"""
        self.transactions.append(transaction)
        self.updated_at = datetime.utcnow()
    
    def get_open_positions(self) -> List[Position]:
        """获取所有开仓持仓"""
        return [pos for pos in self.positions.values() if pos.is_open]
    
    def get_symbols(self) -> Set[str]:
        """获取所有交易品种"""
        return set(self.positions.keys())
    
    def calculate_performance(self) -> PerformanceMetrics:
        """计算投资组合表现"""
        # 这里是简化版本，实际实现会更复杂
        if not self.transactions:
            return PerformanceMetrics()
        
        # 计算总收益
        initial_value = self.account.initial_balance.value
        current_value = self.total_value.value
        
        if initial_value == 0:
            total_return = 0.0
        else:
            total_return = float((current_value - initial_value) / initial_value)
        
        # 计算其他指标（简化版本）
        return PerformanceMetrics(
            total_return=total_return,
            # 其他指标需要更复杂的计算
        )
