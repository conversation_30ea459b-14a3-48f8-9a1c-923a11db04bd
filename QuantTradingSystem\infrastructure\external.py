"""
外部服务模块

提供与外部系统集成的服务，包括通知服务、第三方API等。
"""

import asyncio
import smtplib
import aiohttp
from abc import ABC, abstractmethod
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from shared.exceptions import SystemException
from shared.utils import Logger
from shared.constants import NotificationType
from .config import ConfigurationManager


@dataclass
class NotificationMessage:
    """通知消息"""
    title: str
    content: str
    notification_type: NotificationType
    timestamp: datetime
    metadata: Dict[str, Any]


class INotificationService(ABC):
    """通知服务接口"""
    
    @abstractmethod
    async def send_notification(self, message: NotificationMessage) -> bool:
        """发送通知"""
        pass
    
    @abstractmethod
    async def send_batch_notifications(self, messages: List[NotificationMessage]) -> List[bool]:
        """批量发送通知"""
        pass


class EmailNotificationService(INotificationService):
    """邮件通知服务"""
    
    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("email_notification")
        
        # 获取邮件配置
        self.smtp_server = config.get_config("system.notifications.email.smtp_server", "")
        self.smtp_port = config.get_config("system.notifications.email.smtp_port", 587)
        self.use_tls = config.get_config("system.notifications.email.use_tls", True)
        self.username = config.get_config("EMAIL_USERNAME", "")
        self.password = config.get_config("EMAIL_PASSWORD", "")
        self.from_email = config.get_config("EMAIL_FROM", "")
        self.to_emails = config.get_config("EMAIL_TO", [])
        
        if isinstance(self.to_emails, str):
            self.to_emails = [self.to_emails]
    
    async def send_notification(self, message: NotificationMessage) -> bool:
        """发送邮件通知"""
        try:
            if not self._is_configured():
                self.logger.warning("邮件服务未配置")
                return False
            
            # 创建邮件消息
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = f"[{message.notification_type.value}] {message.title}"
            
            # 邮件内容
            body = self._format_email_body(message)
            msg.attach(MIMEText(body, 'html'))
            
            # 发送邮件
            await self._send_email(msg)
            
            self.logger.info(f"邮件通知发送成功: {message.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送邮件通知失败: {e}")
            return False
    
    async def send_batch_notifications(self, messages: List[NotificationMessage]) -> List[bool]:
        """批量发送邮件通知"""
        results = []
        for message in messages:
            result = await self.send_notification(message)
            results.append(result)
            # 避免发送过快
            await asyncio.sleep(0.1)
        return results
    
    def _is_configured(self) -> bool:
        """检查是否已配置"""
        return all([
            self.smtp_server,
            self.username,
            self.password,
            self.from_email,
            self.to_emails
        ])
    
    async def _send_email(self, msg: MIMEMultipart) -> None:
        """发送邮件"""
        def _send():
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
        
        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, _send)
    
    def _format_email_body(self, message: NotificationMessage) -> str:
        """格式化邮件内容"""
        return f"""
        <html>
        <body>
            <h2>{message.title}</h2>
            <p><strong>类型:</strong> {message.notification_type.value}</p>
            <p><strong>时间:</strong> {message.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</p>
            <hr>
            <div>
                {message.content.replace('\n', '<br>')}
            </div>
            {self._format_metadata(message.metadata)}
        </body>
        </html>
        """
    
    def _format_metadata(self, metadata: Dict[str, Any]) -> str:
        """格式化元数据"""
        if not metadata:
            return ""
        
        html = "<hr><h3>详细信息:</h3><ul>"
        for key, value in metadata.items():
            html += f"<li><strong>{key}:</strong> {value}</li>"
        html += "</ul>"
        return html


class SlackNotificationService(INotificationService):
    """Slack通知服务"""
    
    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("slack_notification")
        
        self.webhook_url = config.get_config("SLACK_WEBHOOK_URL", "")
        self.channel = config.get_config("SLACK_CHANNEL", "#trading")
        self.username = config.get_config("SLACK_USERNAME", "TradingBot")
    
    async def send_notification(self, message: NotificationMessage) -> bool:
        """发送Slack通知"""
        try:
            if not self.webhook_url:
                self.logger.warning("Slack Webhook URL未配置")
                return False
            
            # 构建Slack消息
            slack_message = self._build_slack_message(message)
            
            # 发送消息
            async with aiohttp.ClientSession() as session:
                async with session.post(self.webhook_url, json=slack_message) as response:
                    if response.status == 200:
                        self.logger.info(f"Slack通知发送成功: {message.title}")
                        return True
                    else:
                        self.logger.error(f"Slack通知发送失败: {response.status}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"发送Slack通知失败: {e}")
            return False
    
    async def send_batch_notifications(self, messages: List[NotificationMessage]) -> List[bool]:
        """批量发送Slack通知"""
        results = []
        for message in messages:
            result = await self.send_notification(message)
            results.append(result)
            await asyncio.sleep(0.1)
        return results
    
    def _build_slack_message(self, message: NotificationMessage) -> Dict[str, Any]:
        """构建Slack消息"""
        color = self._get_color_by_type(message.notification_type)
        
        return {
            "channel": self.channel,
            "username": self.username,
            "attachments": [
                {
                    "color": color,
                    "title": message.title,
                    "text": message.content,
                    "fields": [
                        {
                            "title": "类型",
                            "value": message.notification_type.value,
                            "short": True
                        },
                        {
                            "title": "时间",
                            "value": message.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                            "short": True
                        }
                    ] + self._format_metadata_fields(message.metadata),
                    "footer": "量化交易系统",
                    "ts": int(message.timestamp.timestamp())
                }
            ]
        }
    
    def _get_color_by_type(self, notification_type: NotificationType) -> str:
        """根据通知类型获取颜色"""
        color_map = {
            NotificationType.INFO: "good",
            NotificationType.WARNING: "warning", 
            NotificationType.ERROR: "danger",
            NotificationType.SIGNAL: "#36a64f",
            NotificationType.ORDER: "#439FE0",
            NotificationType.POSITION: "#9C27B0",
            NotificationType.SYSTEM: "#FF9800"
        }
        return color_map.get(notification_type, "good")
    
    def _format_metadata_fields(self, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """格式化元数据字段"""
        fields = []
        for key, value in metadata.items():
            fields.append({
                "title": key,
                "value": str(value),
                "short": True
            })
        return fields


class TelegramNotificationService(INotificationService):
    """Telegram通知服务"""
    
    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("telegram_notification")
        
        self.bot_token = config.get_config("TELEGRAM_BOT_TOKEN", "")
        self.chat_id = config.get_config("TELEGRAM_CHAT_ID", "")
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
    
    async def send_notification(self, message: NotificationMessage) -> bool:
        """发送Telegram通知"""
        try:
            if not self.bot_token or not self.chat_id:
                self.logger.warning("Telegram配置不完整")
                return False
            
            # 构建消息
            text = self._format_telegram_message(message)
            
            # 发送消息
            url = f"{self.base_url}/sendMessage"
            data = {
                "chat_id": self.chat_id,
                "text": text,
                "parse_mode": "Markdown"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        self.logger.info(f"Telegram通知发送成功: {message.title}")
                        return True
                    else:
                        self.logger.error(f"Telegram通知发送失败: {response.status}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"发送Telegram通知失败: {e}")
            return False
    
    async def send_batch_notifications(self, messages: List[NotificationMessage]) -> List[bool]:
        """批量发送Telegram通知"""
        results = []
        for message in messages:
            result = await self.send_notification(message)
            results.append(result)
            await asyncio.sleep(0.1)
        return results
    
    def _format_telegram_message(self, message: NotificationMessage) -> str:
        """格式化Telegram消息"""
        text = f"*{message.title}*\n\n"
        text += f"📊 *类型:* {message.notification_type.value}\n"
        text += f"🕐 *时间:* {message.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        text += f"{message.content}\n"
        
        if message.metadata:
            text += "\n*详细信息:*\n"
            for key, value in message.metadata.items():
                text += f"• *{key}:* {value}\n"
        
        return text


class NotificationManager:
    """通知管理器"""
    
    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("notification_manager")
        
        # 初始化通知服务
        self.services: Dict[str, INotificationService] = {}
        self._initialize_services()
        
        # 通知配置
        self.enabled = config.get_config("system.notifications.enabled", True)
        self.channels = config.get_config("system.notifications.channels", ["console"])
    
    def _initialize_services(self) -> None:
        """初始化通知服务"""
        try:
            # 邮件服务
            self.services["email"] = EmailNotificationService(self.config)
            
            # Slack服务
            self.services["slack"] = SlackNotificationService(self.config)
            
            # Telegram服务
            self.services["telegram"] = TelegramNotificationService(self.config)
            
            self.logger.info("通知服务已初始化")
            
        except Exception as e:
            self.logger.error(f"初始化通知服务失败: {e}")
    
    async def send_notification(
        self, 
        title: str,
        content: str,
        notification_type: NotificationType = NotificationType.INFO,
        metadata: Optional[Dict[str, Any]] = None,
        channels: Optional[List[str]] = None
    ) -> Dict[str, bool]:
        """发送通知"""
        if not self.enabled:
            return {}
        
        # 创建通知消息
        message = NotificationMessage(
            title=title,
            content=content,
            notification_type=notification_type,
            timestamp=datetime.now(),
            metadata=metadata or {}
        )
        
        # 确定发送渠道
        target_channels = channels or self.channels
        
        # 发送通知
        results = {}
        for channel in target_channels:
            if channel == "console":
                self._send_console_notification(message)
                results[channel] = True
            elif channel in self.services:
                try:
                    result = await self.services[channel].send_notification(message)
                    results[channel] = result
                except Exception as e:
                    self.logger.error(f"通知发送失败 {channel}: {e}")
                    results[channel] = False
            else:
                self.logger.warning(f"未知的通知渠道: {channel}")
                results[channel] = False
        
        return results
    
    def _send_console_notification(self, message: NotificationMessage) -> None:
        """发送控制台通知"""
        level_map = {
            NotificationType.INFO: "info",
            NotificationType.WARNING: "warning",
            NotificationType.ERROR: "error",
            NotificationType.SIGNAL: "info",
            NotificationType.ORDER: "info",
            NotificationType.POSITION: "info",
            NotificationType.SYSTEM: "info"
        }
        
        level = level_map.get(message.notification_type, "info")
        log_message = f"[{message.notification_type.value}] {message.title}: {message.content}"
        
        getattr(self.logger, level)(log_message)
    
    async def send_signal_notification(
        self, 
        symbol: str, 
        signal_type: str, 
        strength: float,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, bool]:
        """发送信号通知"""
        title = f"交易信号: {symbol}"
        content = f"信号类型: {signal_type}\n信号强度: {strength:.2%}"
        
        return await self.send_notification(
            title=title,
            content=content,
            notification_type=NotificationType.SIGNAL,
            metadata=metadata
        )
    
    async def send_order_notification(
        self, 
        symbol: str, 
        order_type: str, 
        status: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, bool]:
        """发送订单通知"""
        title = f"订单更新: {symbol}"
        content = f"订单类型: {order_type}\n订单状态: {status}"
        
        return await self.send_notification(
            title=title,
            content=content,
            notification_type=NotificationType.ORDER,
            metadata=metadata
        )
    
    async def send_error_notification(
        self, 
        error_message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, bool]:
        """发送错误通知"""
        return await self.send_notification(
            title="系统错误",
            content=error_message,
            notification_type=NotificationType.ERROR,
            metadata=metadata
        )
