"""
工具函数模块

提供系统中使用的各种工具函数和实用类，包括日志、时间处理、数学计算、验证等。
利用Python 3.13的新特性提供更好的性能和功能。
"""

import asyncio
import hashlib
import logging
import os
import sys
import time
from datetime import datetime, timedelta, timezone
from functools import wraps
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union
import threading
import concurrent.futures

# Python 3.13 新特性检查
HAS_FREE_THREADING = hasattr(sys, "_is_gil_enabled")
IS_FREE_THREADED = HAS_FREE_THREADING and not sys._is_gil_enabled()

T = TypeVar('T')


class Logger:
    """
    日志管理器
    
    提供统一的日志管理功能，支持文件和控制台输出。
    """
    
    _loggers: Dict[str, logging.Logger] = {}
    _initialized = False
    
    @classmethod
    def initialize(
        cls,
        level: int = logging.INFO,
        file_path: Optional[str] = None,
        format: Optional[str] = None,
        max_file_size: str = "10MB",
        backup_count: int = 5
    ) -> None:
        """初始化日志系统"""
        if cls._initialized:
            return
        
        # 设置默认格式
        if format is None:
            format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        # 创建根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_formatter = logging.Formatter(format)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器
        if file_path:
            from logging.handlers import RotatingFileHandler
            
            # 确保日志目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 解析文件大小
            max_bytes = cls._parse_size(max_file_size)
            
            file_handler = RotatingFileHandler(
                file_path,
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(level)
            file_formatter = logging.Formatter(format)
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
        
        cls._initialized = True
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """获取指定名称的日志记录器"""
        if name not in cls._loggers:
            cls._loggers[name] = logging.getLogger(name)
        return cls._loggers[name]
    
    @staticmethod
    def _parse_size(size_str: str) -> int:
        """解析大小字符串为字节数"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)


class DateTimeUtils:
    """日期时间工具类"""
    
    @staticmethod
    def now_utc() -> datetime:
        """获取当前UTC时间"""
        return datetime.now(timezone.utc)
    
    @staticmethod
    def to_timestamp(dt: datetime) -> float:
        """将datetime转换为时间戳"""
        return dt.timestamp()
    
    @staticmethod
    def from_timestamp(timestamp: float) -> datetime:
        """从时间戳创建datetime"""
        return datetime.fromtimestamp(timestamp, timezone.utc)
    
    @staticmethod
    def format_datetime(dt: datetime, format: str = "%Y-%m-%d %H:%M:%S") -> str:
        """格式化datetime"""
        return dt.strftime(format)
    
    @staticmethod
    def parse_datetime(dt_str: str, format: str = "%Y-%m-%d %H:%M:%S") -> datetime:
        """解析datetime字符串"""
        return datetime.strptime(dt_str, format).replace(tzinfo=timezone.utc)
    
    @staticmethod
    def get_trading_days(start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取交易日列表（排除周末）"""
        trading_days = []
        current_date = start_date
        
        while current_date <= end_date:
            # 排除周末（周六=5，周日=6）
            if current_date.weekday() < 5:
                trading_days.append(current_date)
            current_date += timedelta(days=1)
        
        return trading_days
    
    @staticmethod
    def is_market_hours(dt: datetime, market_open: str = "09:30", market_close: str = "16:00") -> bool:
        """检查是否在市场交易时间内"""
        time_str = dt.strftime("%H:%M")
        return market_open <= time_str <= market_close


class MathUtils:
    """数学计算工具类"""
    
    @staticmethod
    def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
        """安全除法，避免除零错误"""
        return numerator / denominator if denominator != 0 else default
    
    @staticmethod
    def percentage_change(old_value: float, new_value: float) -> float:
        """计算百分比变化"""
        if old_value == 0:
            return 0.0
        return (new_value - old_value) / old_value
    
    @staticmethod
    def compound_return(returns: List[float]) -> float:
        """计算复合收益率"""
        if not returns:
            return 0.0
        
        compound = 1.0
        for ret in returns:
            compound *= (1 + ret)
        return compound - 1.0
    
    @staticmethod
    def annualized_return(total_return: float, days: int) -> float:
        """计算年化收益率"""
        if days <= 0:
            return 0.0
        return (1 + total_return) ** (365.25 / days) - 1
    
    @staticmethod
    def sharpe_ratio(returns: List[float], risk_free_rate: float = 0.0) -> float:
        """计算夏普比率"""
        if not returns or len(returns) < 2:
            return 0.0
        
        import statistics
        mean_return = statistics.mean(returns)
        std_return = statistics.stdev(returns)
        
        if std_return == 0:
            return 0.0
        
        return (mean_return - risk_free_rate) / std_return
    
    @staticmethod
    def max_drawdown(values: List[float]) -> float:
        """计算最大回撤"""
        if not values:
            return 0.0
        
        peak = values[0]
        max_dd = 0.0
        
        for value in values:
            if value > peak:
                peak = value
            else:
                drawdown = (peak - value) / peak
                max_dd = max(max_dd, drawdown)
        
        return max_dd


class ValidationUtils:
    """验证工具类"""
    
    @staticmethod
    def is_valid_symbol(symbol: str) -> bool:
        """验证交易品种代码"""
        if not symbol or not isinstance(symbol, str):
            return False
        return symbol.isalnum() and len(symbol) >= 2
    
    @staticmethod
    def is_valid_price(price: float) -> bool:
        """验证价格"""
        return isinstance(price, (int, float)) and price > 0
    
    @staticmethod
    def is_valid_quantity(quantity: float) -> bool:
        """验证数量"""
        return isinstance(quantity, (int, float)) and quantity > 0
    
    @staticmethod
    def is_valid_percentage(value: float) -> bool:
        """验证百分比（0-1之间）"""
        return isinstance(value, (int, float)) and 0 <= value <= 1
    
    @staticmethod
    def validate_config(config: Dict[str, Any], required_keys: List[str]) -> bool:
        """验证配置字典"""
        return all(key in config for key in required_keys)


class PerformanceUtils:
    """性能工具类"""
    
    @staticmethod
    def timing_decorator(func: Callable[..., T]) -> Callable[..., T]:
        """性能计时装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs) -> T:
            start_time = time.perf_counter()
            result = func(*args, **kwargs)
            end_time = time.perf_counter()
            
            logger = Logger.get_logger("performance")
            logger.debug(f"{func.__name__} 执行时间: {end_time - start_time:.4f}秒")
            
            return result
        return wrapper
    
    @staticmethod
    def async_timing_decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        """异步性能计时装饰器"""
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            start_time = time.perf_counter()
            result = await func(*args, **kwargs)
            end_time = time.perf_counter()
            
            logger = Logger.get_logger("performance")
            logger.debug(f"{func.__name__} 执行时间: {end_time - start_time:.4f}秒")
            
            return result
        return wrapper
    
    @staticmethod
    def memory_usage() -> Dict[str, float]:
        """获取内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return {
                "rss": memory_info.rss / 1024 / 1024,  # MB
                "vms": memory_info.vms / 1024 / 1024,  # MB
                "percent": process.memory_percent()
            }
        except ImportError:
            return {"error": "psutil not available"}
    
    @staticmethod
    def get_optimal_worker_count() -> int:
        """获取最优工作线程数"""
        if IS_FREE_THREADED:
            # Python 3.13 自由线程模式下可以使用更多线程
            return min(os.cpu_count() * 2, 16)
        else:
            # 传统GIL模式下限制线程数
            return min(os.cpu_count(), 8)


class CacheUtils:
    """缓存工具类"""
    
    @staticmethod
    def generate_cache_key(*args, **kwargs) -> str:
        """生成缓存键"""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    @staticmethod
    def simple_cache(maxsize: int = 128, ttl: int = 3600):
        """简单缓存装饰器"""
        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            cache: Dict[str, tuple] = {}
            
            @wraps(func)
            def wrapper(*args, **kwargs) -> T:
                key = CacheUtils.generate_cache_key(*args, **kwargs)
                current_time = time.time()
                
                # 检查缓存
                if key in cache:
                    value, timestamp = cache[key]
                    if current_time - timestamp < ttl:
                        return value
                
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                
                # 清理过期缓存
                if len(cache) >= maxsize:
                    expired_keys = [
                        k for k, (_, ts) in cache.items()
                        if current_time - ts >= ttl
                    ]
                    for k in expired_keys:
                        del cache[k]
                
                cache[key] = (result, current_time)
                return result
            
            return wrapper
        return decorator


class AsyncUtils:
    """异步工具类"""
    
    @staticmethod
    async def run_with_timeout(coro, timeout: float):
        """运行协程并设置超时"""
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            raise TimeoutError(f"操作超时: {timeout}秒")
    
    @staticmethod
    async def gather_with_concurrency(tasks: List, max_concurrency: int = 10):
        """限制并发数的gather"""
        semaphore = asyncio.Semaphore(max_concurrency)
        
        async def run_task(task):
            async with semaphore:
                return await task
        
        return await asyncio.gather(*[run_task(task) for task in tasks])
    
    @staticmethod
    def run_in_thread_pool(func: Callable, *args, max_workers: Optional[int] = None, **kwargs):
        """在线程池中运行函数"""
        if max_workers is None:
            max_workers = PerformanceUtils.get_optimal_worker_count()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future = executor.submit(func, *args, **kwargs)
            return future.result()


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def ensure_directory(path: Union[str, Path]) -> Path:
        """确保目录存在"""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def safe_file_write(file_path: Union[str, Path], content: str, encoding: str = "utf-8") -> None:
        """安全写入文件"""
        file_path = Path(file_path)
        FileUtils.ensure_directory(file_path.parent)
        
        # 先写入临时文件，然后重命名
        temp_path = file_path.with_suffix(file_path.suffix + ".tmp")
        try:
            with open(temp_path, 'w', encoding=encoding) as f:
                f.write(content)
            temp_path.replace(file_path)
        except Exception:
            if temp_path.exists():
                temp_path.unlink()
            raise
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """获取文件大小（字节）"""
        return Path(file_path).stat().st_size
    
    @staticmethod
    def is_file_locked(file_path: Union[str, Path]) -> bool:
        """检查文件是否被锁定"""
        try:
            with open(file_path, 'a'):
                return False
        except IOError:
            return True
