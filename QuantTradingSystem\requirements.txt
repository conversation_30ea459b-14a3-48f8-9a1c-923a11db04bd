# 量化交易系统依赖包

# 核心依赖
pandas>=2.1.0
numpy>=1.24.0
scipy>=1.11.0

# 数据获取
yfinance>=0.2.18
requests>=2.31.0
aiohttp>=3.8.0
websockets>=11.0.0

# 数据处理和分析
ta-lib>=0.4.25
scikit-learn>=1.3.0

# 配置管理
PyYAML>=6.0

# 异步支持
asyncio-mqtt>=0.13.0

# GUI框架
# tkinter  # 通常随Python安装

# 可选的高级优化库
# scikit-optimize>=0.9.0  # 贝叶斯优化
# optuna>=3.3.0          # 超参数优化

# 数据库支持（可选）
# sqlite3  # 通常随Python安装
# sqlalchemy>=2.0.0

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0

# 开发工具
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# 文档生成（可选）
# sphinx>=7.0.0
# sphinx-rtd-theme>=1.3.0

# 性能分析（可选）
# memory-profiler>=0.61.0
# line-profiler>=4.1.0

# 加密货币数据源（可选）
# python-binance>=1.0.17
# ccxt>=4.0.0

# 通知服务（可选）
# slack-sdk>=3.21.0
# python-telegram-bot>=20.0.0

# 图表和可视化（可选）
# matplotlib>=3.7.0
# plotly>=5.15.0
# mplfinance>=0.12.0

# 时间序列分析（可选）
# statsmodels>=0.14.0
# arch>=6.2.0

# 机器学习（可选）
# tensorflow>=2.13.0
# torch>=2.0.0
# xgboost>=1.7.0

# 风险管理（可选）
# pyfolio>=0.9.2
# empyrical>=0.5.5

# 数据存储（可选）
# redis>=4.6.0
# pymongo>=4.4.0
# influxdb-client>=1.37.0
