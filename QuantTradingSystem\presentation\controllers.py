"""
控制器模块

MVC架构中的控制器 - 协调视图和业务逻辑。
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
import threading

from shared.utils import Logger
from infrastructure.config import ConfigurationManager
from infrastructure.external import NotificationManager
from application.services import (
    DataService, StrategyService, BacktestService, PortfolioService
)
from application.orchestrators import TradingOrchestrator, BacktestOrchestrator


class MainController:
    """
    主控制器
    
    协调所有业务逻辑和用户界面交互。
    """
    
    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("main_controller")
        
        # 初始化服务
        self._initialize_services()
        
        # UI相关
        self.main_window = None
        self.async_tk = None
        
        # 状态
        self._is_running = False
        self._lock = threading.RLock()
    
    def _initialize_services(self):
        """初始化服务"""
        try:
            # 基础服务
            self.data_service = DataService(self.config)
            self.strategy_service = StrategyService(self.config, self.data_service)
            self.backtest_service = BacktestService(self.config, self.data_service)
            self.portfolio_service = PortfolioService(self.config)
            
            # 通知服务
            self.notification_manager = NotificationManager(self.config)
            
            # 协调器
            self.trading_orchestrator = TradingOrchestrator(
                self.config,
                self.data_service,
                self.strategy_service,
                self.portfolio_service,
                self.notification_manager
            )
            
            self.backtest_orchestrator = BacktestOrchestrator(
                self.config,
                self.data_service,
                self.backtest_service,
                self.notification_manager
            )
            
            self.logger.info("服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    async def start(self):
        """启动控制器"""
        with self._lock:
            if self._is_running:
                return
            
            self._is_running = True
        
        try:
            # 启动服务
            await self.data_service.start()
            await self.strategy_service.start()
            await self.backtest_service.start()
            await self.portfolio_service.start()
            
            # 启动协调器
            await self.trading_orchestrator.start()
            await self.backtest_orchestrator.start()
            
            self.logger.info("控制器已启动")
            
        except Exception as e:
            self.logger.error(f"控制器启动失败: {e}")
            raise
    
    async def stop(self):
        """停止控制器"""
        with self._lock:
            if not self._is_running:
                return
            
            self._is_running = False
        
        try:
            # 停止协调器
            await self.backtest_orchestrator.stop()
            await self.trading_orchestrator.stop()
            
            # 停止服务
            await self.portfolio_service.stop()
            await self.backtest_service.stop()
            await self.strategy_service.stop()
            await self.data_service.stop()
            
            self.logger.info("控制器已停止")
            
        except Exception as e:
            self.logger.error(f"控制器停止失败: {e}")
    
    # 策略相关方法
    async def load_strategy(self, strategy_name: str, config: Optional[Dict[str, Any]] = None) -> bool:
        """加载策略"""
        try:
            await self.strategy_service.load_strategy(strategy_name, config)
            return True
        except Exception as e:
            self.logger.error(f"加载策略失败: {e}")
            return False
    
    async def start_strategy(self, strategy_name: str) -> bool:
        """启动策略"""
        try:
            strategy_info = self.strategy_service.get_strategy_info(strategy_name)
            if strategy_info and strategy_info.get('instance'):
                await strategy_info['instance'].start()
                return True
            return False
        except Exception as e:
            self.logger.error(f"启动策略失败: {e}")
            return False
    
    async def stop_strategy(self, strategy_name: str) -> bool:
        """停止策略"""
        try:
            strategy_info = self.strategy_service.get_strategy_info(strategy_name)
            if strategy_info and strategy_info.get('instance'):
                await strategy_info['instance'].stop()
                return True
            return False
        except Exception as e:
            self.logger.error(f"停止策略失败: {e}")
            return False
    
    async def unload_strategy(self, strategy_name: str) -> bool:
        """卸载策略"""
        try:
            return await self.strategy_service.unload_strategy(strategy_name)
        except Exception as e:
            self.logger.error(f"卸载策略失败: {e}")
            return False
    
    def get_strategies(self) -> Dict[str, Any]:
        """获取所有策略信息"""
        return self.strategy_service.get_all_strategies()
    
    def get_strategy_config(self, strategy_name: str) -> Dict[str, Any]:
        """获取策略配置"""
        strategy_info = self.strategy_service.get_strategy_info(strategy_name)
        if strategy_info:
            return strategy_info.get('config', {})
        return {}
    
    async def update_strategy_config(self, strategy_name: str, config: Dict[str, Any]) -> bool:
        """更新策略配置"""
        try:
            strategy_info = self.strategy_service.get_strategy_info(strategy_name)
            if strategy_info and strategy_info.get('instance'):
                await strategy_info['instance'].update_parameters(config)
                return True
            return False
        except Exception as e:
            self.logger.error(f"更新策略配置失败: {e}")
            return False
    
    # 回测相关方法
    async def run_backtest(
        self,
        strategy_name: str,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        initial_capital: float = 100000.0,
        progress_callback: Optional[Callable[[float], None]] = None
    ):
        """运行回测"""
        try:
            result = await self.backtest_service.run_backtest(
                strategy_name=strategy_name,
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital,
                progress_callback=progress_callback
            )
            return result
        except Exception as e:
            self.logger.error(f"回测失败: {e}")
            raise
    
    async def run_comprehensive_backtest(
        self,
        strategy_name: str,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        progress_callback: Optional[Callable[[str, float], None]] = None
    ) -> Dict[str, Any]:
        """运行综合回测"""
        try:
            result = await self.backtest_orchestrator.run_comprehensive_backtest(
                strategy_name=strategy_name,
                symbols=symbols,
                start_date=start_date,
                end_date=end_date,
                progress_callback=progress_callback
            )
            return result
        except Exception as e:
            self.logger.error(f"综合回测失败: {e}")
            raise
    
    async def optimize_parameters(
        self,
        strategy_name: str,
        symbol: str,
        parameters: Dict[str, Any],
        start_date: datetime,
        end_date: datetime,
        method: str = "grid_search",
        progress_callback: Optional[Callable[[float], None]] = None
    ):
        """优化参数"""
        try:
            result = await self.backtest_service.optimize_parameters(
                strategy_name=strategy_name,
                symbol=symbol,
                parameters=parameters,
                start_date=start_date,
                end_date=end_date,
                method=method,
                progress_callback=progress_callback
            )
            return result
        except Exception as e:
            self.logger.error(f"参数优化失败: {e}")
            raise
    
    def get_running_backtests(self) -> Dict[str, Any]:
        """获取正在运行的回测"""
        return self.backtest_service.get_running_backtests()
    
    def get_running_optimizations(self) -> Dict[str, Any]:
        """获取正在运行的优化"""
        return self.backtest_service.get_running_optimizations()
    
    # 投资组合相关方法
    def create_portfolio(self, name: str, initial_capital: float):
        """创建投资组合"""
        try:
            return self.portfolio_service.create_portfolio(name, initial_capital)
        except Exception as e:
            self.logger.error(f"创建投资组合失败: {e}")
            raise
    
    def get_portfolios(self) -> Dict[str, Any]:
        """获取所有投资组合"""
        portfolios = self.portfolio_service.get_all_portfolios()
        result = {}
        for portfolio_id, portfolio in portfolios.items():
            result[portfolio_id] = self.portfolio_service.get_portfolio_summary(portfolio_id)
        return result
    
    def get_portfolio_summary(self, portfolio_id: str) -> Optional[Dict[str, Any]]:
        """获取投资组合摘要"""
        return self.portfolio_service.get_portfolio_summary(portfolio_id)
    
    def delete_portfolio(self, portfolio_id: str) -> bool:
        """删除投资组合"""
        return self.portfolio_service.delete_portfolio(portfolio_id)
    
    # 交易会话相关方法
    async def start_trading_session(
        self,
        portfolio_id: str,
        symbols: List[str],
        strategies: List[str],
        paper_trading: bool = True
    ) -> str:
        """启动交易会话"""
        try:
            session_id = await self.trading_orchestrator.start_trading_session(
                portfolio_id=portfolio_id,
                symbols=symbols,
                strategies=strategies,
                paper_trading=paper_trading
            )
            return session_id
        except Exception as e:
            self.logger.error(f"启动交易会话失败: {e}")
            raise
    
    async def stop_trading_session(self, session_id: str) -> bool:
        """停止交易会话"""
        try:
            return await self.trading_orchestrator.stop_trading_session(session_id)
        except Exception as e:
            self.logger.error(f"停止交易会话失败: {e}")
            return False
    
    def get_active_sessions(self) -> Dict[str, Any]:
        """获取活跃的交易会话"""
        sessions = self.trading_orchestrator.get_active_sessions()
        result = {}
        for session_id, session in sessions.items():
            result[session_id] = {
                'session_id': session.session_id,
                'portfolio_id': session.portfolio_id,
                'symbols': session.symbols,
                'strategies': session.strategies,
                'start_time': session.start_time,
                'is_active': session.is_active,
                'paper_trading': session.paper_trading
            }
        return result
    
    # 数据相关方法
    async def get_historical_data(
        self,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime,
        source: Optional[str] = None
    ):
        """获取历史数据"""
        try:
            from shared.constants import TimeFrame
            tf = TimeFrame(timeframe)
            return await self.data_service.get_historical_data(
                symbol, tf, start_date, end_date, source
            )
        except Exception as e:
            self.logger.error(f"获取历史数据失败: {e}")
            raise
    
    def get_available_symbols(self, source: Optional[str] = None) -> List[str]:
        """获取可用的交易品种"""
        return self.data_service.get_available_symbols(source)
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return self.data_service.get_cache_info()
    
    def clear_cache(self):
        """清空缓存"""
        self.data_service.clear_cache()
    
    # 系统状态相关方法
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'data_service': self.data_service.get_status().__dict__,
            'strategy_service': self.strategy_service.get_status().__dict__,
            'backtest_service': self.backtest_service.get_status().__dict__,
            'portfolio_service': self.portfolio_service.get_status().__dict__,
            'trading_orchestrator': self.trading_orchestrator.get_status().__dict__,
            'backtest_orchestrator': self.backtest_orchestrator.get_status().__dict__,
            'is_running': self._is_running
        }
    
    def get_signal_processing_stats(self) -> Dict[str, Any]:
        """获取信号处理统计"""
        return self.strategy_service.get_signal_processing_stats()
    
    # 配置相关方法
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置"""
        return self.config.get_config(key, default)
    
    def set_config(self, key: str, value: Any):
        """设置配置"""
        self.config.set_config(key, value)
    
    def save_config(self, file_name: str):
        """保存配置到文件"""
        self.config.save_config_to_file(file_name)
    
    def reload_config(self):
        """重新加载配置"""
        self.config.reload_config()
    
    # UI相关方法
    def set_main_window(self, window):
        """设置主窗口引用"""
        self.main_window = window
    
    def set_async_tk(self, async_tk):
        """设置异步Tkinter包装器"""
        self.async_tk = async_tk
