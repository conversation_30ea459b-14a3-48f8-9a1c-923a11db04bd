"""
常量定义模块

定义系统中使用的所有常量和枚举值，提供统一的常量管理。
"""

from enum import Enum, auto
from typing import Dict, Any


class SignalType(Enum):
    """交易信号类型"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    STRONG_BUY = "STRONG_BUY"
    STRONG_SELL = "STRONG_SELL"
    EXIT = "EXIT"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"


class OrderType(Enum):
    """订单类型"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"
    TRAILING_STOP = "TRAILING_STOP"
    OCO = "OCO"  # One-Cancels-the-Other


class OrderStatus(Enum):
    """订单状态"""
    CREATED = "CREATED"
    SUBMITTED = "SUBMITTED"
    PARTIAL_FILLED = "PARTIAL_FILLED"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"


class OrderSide(Enum):
    """订单方向"""
    BUY = "BUY"
    SELL = "SELL"


class TimeFrame(Enum):
    """时间周期"""
    TICK = "TICK"
    M1 = "1m"    # 1分钟
    M3 = "3m"    # 3分钟
    M5 = "5m"    # 5分钟
    M15 = "15m"  # 15分钟
    M30 = "30m"  # 30分钟
    H1 = "1h"    # 1小时
    H2 = "2h"    # 2小时
    H4 = "4h"    # 4小时
    H6 = "6h"    # 6小时
    H8 = "8h"    # 8小时
    H12 = "12h"  # 12小时
    D1 = "1d"    # 1天
    D3 = "3d"    # 3天
    W1 = "1w"    # 1周
    MON1 = "1M"  # 1月


class MarketStatus(Enum):
    """市场状态"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PRE_MARKET = "PRE_MARKET"
    POST_MARKET = "POST_MARKET"
    HALTED = "HALTED"


class AssetType(Enum):
    """资产类型"""
    STOCK = "STOCK"
    CRYPTO = "CRYPTO"
    FOREX = "FOREX"
    FUTURES = "FUTURES"
    OPTIONS = "OPTIONS"
    BOND = "BOND"
    ETF = "ETF"
    INDEX = "INDEX"


class PositionSide(Enum):
    """持仓方向"""
    LONG = "LONG"
    SHORT = "SHORT"
    NEUTRAL = "NEUTRAL"


class PositionStatus(Enum):
    """持仓状态"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PARTIALLY_CLOSED = "PARTIALLY_CLOSED"


class RiskLevel(Enum):
    """风险等级"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    EXTREME = "EXTREME"


class StrategyStatus(Enum):
    """策略状态"""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    PAUSED = "PAUSED"
    ERROR = "ERROR"
    OPTIMIZING = "OPTIMIZING"
    BACKTESTING = "BACKTESTING"


class BacktestStatus(Enum):
    """回测状态"""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class OptimizationMethod(Enum):
    """优化方法"""
    GRID_SEARCH = "GRID_SEARCH"
    RANDOM_SEARCH = "RANDOM_SEARCH"
    BAYESIAN = "BAYESIAN"
    GENETIC = "GENETIC"
    PARTICLE_SWARM = "PARTICLE_SWARM"


class PerformanceMetric(Enum):
    """性能指标"""
    TOTAL_RETURN = "TOTAL_RETURN"
    ANNUALIZED_RETURN = "ANNUALIZED_RETURN"
    SHARPE_RATIO = "SHARPE_RATIO"
    SORTINO_RATIO = "SORTINO_RATIO"
    MAX_DRAWDOWN = "MAX_DRAWDOWN"
    CALMAR_RATIO = "CALMAR_RATIO"
    VOLATILITY = "VOLATILITY"
    ALPHA = "ALPHA"
    BETA = "BETA"
    INFORMATION_RATIO = "INFORMATION_RATIO"
    WIN_RATE = "WIN_RATE"
    PROFIT_FACTOR = "PROFIT_FACTOR"
    EXPECTANCY = "EXPECTANCY"


class NotificationType(Enum):
    """通知类型"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    SIGNAL = "SIGNAL"
    ORDER = "ORDER"
    POSITION = "POSITION"
    SYSTEM = "SYSTEM"


class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class DataSourceType(Enum):
    """数据源类型"""
    EXCHANGE = "EXCHANGE"
    API = "API"
    DATABASE = "DATABASE"
    FILE = "FILE"
    WEBSOCKET = "WEBSOCKET"
    SIMULATION = "SIMULATION"


class CachePolicy(Enum):
    """缓存策略"""
    NO_CACHE = "NO_CACHE"
    MEMORY = "MEMORY"
    DISK = "DISK"
    DISTRIBUTED = "DISTRIBUTED"


# 系统常量
SYSTEM_CONSTANTS = {
    # 时间相关常量
    "SECONDS_IN_MINUTE": 60,
    "SECONDS_IN_HOUR": 3600,
    "SECONDS_IN_DAY": 86400,
    "SECONDS_IN_WEEK": 604800,
    "SECONDS_IN_MONTH": 2592000,  # 30天
    "SECONDS_IN_YEAR": 31536000,  # 365天
    
    # 交易相关常量
    "MAX_ORDER_RETRY": 3,
    "DEFAULT_ORDER_TIMEOUT": 30,  # 秒
    "DEFAULT_POSITION_SIZE": 0.1,  # 10%
    "DEFAULT_STOP_LOSS": 0.02,    # 2%
    "DEFAULT_TAKE_PROFIT": 0.04,  # 4%
    
    # 回测相关常量
    "DEFAULT_INITIAL_CAPITAL": 100000,
    "DEFAULT_COMMISSION": 0.001,  # 0.1%
    "DEFAULT_SLIPPAGE": 0.0001,   # 0.01%
    
    # 风险管理相关常量
    "MAX_DRAWDOWN_THRESHOLD": 0.2,  # 20%
    "MAX_POSITION_SIZE": 0.25,      # 25%
    "MAX_POSITIONS": 10,
    "MAX_CORRELATION": 0.7,
    
    # 性能相关常量
    "CACHE_TTL": 3600,  # 1小时
    "MAX_CACHE_SIZE": 1000,
    "MAX_WORKERS": 4,
    
    # 数据相关常量
    "DEFAULT_TIMEFRAME": TimeFrame.D1.value,
    "MAX_HISTORY_DAYS": 365,
    "DATA_BATCH_SIZE": 1000,
    
    # 系统相关常量
    "VERSION": "1.0.0",
    "API_VERSION": "v1",
    "DEFAULT_ENCODING": "utf-8",
    "DEFAULT_TIMEZONE": "UTC",
}


# 默认配置
DEFAULT_CONFIG: Dict[str, Any] = {
    "system": {
        "logging": {
            "level": "INFO",
            "file_path": "logs/system.log",
            "max_file_size": "10MB",
            "backup_count": 5,
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        },
        "performance": {
            "cache_size": 1000,
            "max_workers": 4,
            "enable_jit": False,
            "enable_free_threading": False
        },
        "database": {
            "type": "sqlite",
            "path": "data/trading_system.db",
            "pool_size": 10,
            "echo": False
        }
    },
    "data_sources": {
        "binance": {
            "enabled": True,
            "use_testnet": True,
            "rate_limit": 1200,
            "timeout": 30,
            "retry_attempts": 3
        },
        "yahoo_finance": {
            "enabled": True,
            "timeout": 30,
            "retry_attempts": 3
        }
    },
    "strategies": {
        "default": {
            "sma_cross": {
                "enabled": True,
                "fast_period": 10,
                "slow_period": 30,
                "signal_threshold": 0.01,
                "position_size": 0.1,
                "stop_loss": 0.02,
                "take_profit": 0.04
            }
        }
    }
}
