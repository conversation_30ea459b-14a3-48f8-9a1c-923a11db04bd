# 策略配置模板
# 复制此文件为 strategies.yaml 并根据需要修改配置

strategies:
  # 默认策略配置
  default:
    # 简单移动平均交叉策略
    sma_cross:
      enabled: true
      fast_period: 10
      slow_period: 30
      signal_threshold: 0.01
      position_size: 0.1
      stop_loss: 0.02
      take_profit: 0.04
      
    # 相对强弱指数策略
    rsi_strategy:
      enabled: false
      period: 14
      oversold_threshold: 30
      overbought_threshold: 70
      position_size: 0.1
      
    # 布林带策略
    bollinger_bands:
      enabled: false
      period: 20
      std_dev: 2.0
      position_size: 0.1
      
    # MACD策略
    macd_strategy:
      enabled: false
      fast_period: 12
      slow_period: 26
      signal_period: 9
      position_size: 0.1
      
  # 策略组合配置
  portfolios:
    conservative:
      strategies:
        - sma_cross
      max_positions: 3
      risk_level: "low"
      
    aggressive:
      strategies:
        - sma_cross
        - rsi_strategy
        - macd_strategy
      max_positions: 5
      risk_level: "high"
      
  # 策略执行配置
  execution:
    # 信号过滤
    signal_filtering:
      enabled: true
      min_signal_strength: 0.6
      max_signals_per_day: 10
      
    # 仓位管理
    position_management:
      max_position_size: 0.2
      position_sizing_method: "fixed"  # fixed, kelly, volatility
      rebalance_frequency: "daily"
      
    # 风险控制
    risk_control:
      max_drawdown: 0.1
      var_threshold: 0.05
      correlation_threshold: 0.8
      
  # 策略优化配置
  optimization:
    enabled: true
    method: "grid_search"  # grid_search, random_search, bayesian
    parameters:
      sma_cross:
        fast_period: [5, 10, 15, 20]
        slow_period: [20, 30, 40, 50]
    objective: "sharpe_ratio"  # return, sharpe_ratio, calmar_ratio
    
  # 策略监控配置
  monitoring:
    enabled: true
    performance_metrics:
      - total_return
      - sharpe_ratio
      - max_drawdown
      - win_rate
    alert_thresholds:
      max_drawdown: 0.05
      daily_loss: 0.02
      
  # 机器学习策略配置
  ml_strategies:
    enabled: false
    models:
      lstm_predictor:
        enabled: false
        lookback_period: 60
        prediction_horizon: 1
        retrain_frequency: "weekly"
        
      random_forest:
        enabled: false
        n_estimators: 100
        max_depth: 10
        feature_importance_threshold: 0.01
