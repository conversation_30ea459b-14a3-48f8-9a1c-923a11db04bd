# 系统配置模板
# 复制此文件为 system.yaml 并根据需要修改配置

system:
  # 日志配置
  logging:
    level: INFO
    file_path: "logs/system.log"
    max_file_size: "10MB"
    backup_count: 5
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
  # 性能配置
  performance:
    cache_size: 1000
    max_workers: 4
    enable_jit: false  # Python 3.13 JIT编译器
    enable_free_threading: false  # Python 3.13 自由线程
    
  # 数据库配置
  database:
    type: "sqlite"
    path: "data/trading_system.db"
    pool_size: 10
    echo: false
    
  # 安全配置
  security:
    encrypt_api_keys: true
    session_timeout: 3600
    max_login_attempts: 5
    
  # 通知配置
  notifications:
    enabled: true
    channels:
      - email
      - console
    email:
      smtp_server: "smtp.gmail.com"
      smtp_port: 587
      use_tls: true
      
  # 风险管理配置
  risk_management:
    max_position_size: 0.1  # 最大仓位比例
    max_daily_loss: 0.05    # 最大日损失比例
    stop_loss_threshold: 0.02  # 止损阈值
    
  # 回测配置
  backtesting:
    default_commission: 0.001  # 默认手续费
    slippage: 0.0001          # 滑点
    initial_capital: 100000    # 初始资金
    
  # 实时交易配置
  live_trading:
    enabled: false
    paper_trading: true  # 模拟交易
    order_timeout: 30    # 订单超时时间（秒）
    
  # 缓存配置
  cache:
    type: "memory"  # memory, redis
    ttl: 3600      # 缓存过期时间（秒）
    max_size: 1000 # 最大缓存条目数
