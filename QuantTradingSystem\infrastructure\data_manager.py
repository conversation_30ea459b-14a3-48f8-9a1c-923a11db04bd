"""
数据管理器模块

数据管理核心模块 - 封装所有数据复杂性，提供统一的数据获取接口。
实现深度模块化设计，隐藏数据源的复杂性。
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import threading

from domain.value_objects import MarketData
from shared.constants import TimeFrame
from shared.exceptions import DataSourceException, ValidationException
from shared.utils import Logger, CacheUtils, AsyncUtils
from .data_sources import IDataSource, BinanceDataSource, YahooFinanceDataSource, LocalFileDataSource
from .config import ConfigurationManager


@dataclass
class DataRequest:
    """数据请求对象"""
    symbol: str
    timeframe: TimeFrame
    start_date: datetime
    end_date: datetime
    source: Optional[str] = None
    use_cache: bool = True
    validate_data: bool = True


@dataclass
class MarketDataSet:
    """市场数据集"""
    symbol: str
    timeframe: TimeFrame
    data: List[MarketData]
    source: str
    timestamp: datetime
    metadata: Dict[str, Any]


class DataCache:
    """数据缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl
        self._cache: Dict[str, tuple] = {}
        self._lock = threading.RLock()
        self.logger = Logger.get_logger("data_cache")
    
    def get(self, key: str) -> Optional[MarketDataSet]:
        """获取缓存数据"""
        with self._lock:
            if key in self._cache:
                data, timestamp = self._cache[key]
                if datetime.now().timestamp() - timestamp < self.ttl:
                    self.logger.debug(f"缓存命中: {key}")
                    return data
                else:
                    # 缓存过期，删除
                    del self._cache[key]
                    self.logger.debug(f"缓存过期: {key}")
        return None
    
    def set(self, key: str, data: MarketDataSet) -> None:
        """设置缓存数据"""
        with self._lock:
            # 检查缓存大小
            if len(self._cache) >= self.max_size:
                # 删除最旧的缓存项
                oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k][1])
                del self._cache[oldest_key]
                self.logger.debug(f"缓存已满，删除最旧项: {oldest_key}")
            
            self._cache[key] = (data, datetime.now().timestamp())
            self.logger.debug(f"缓存已设置: {key}")
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self.logger.info("缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        with self._lock:
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'ttl': self.ttl,
                'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_total_count', 1), 1)
            }


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.logger = Logger.get_logger("data_validator")
    
    def validate_market_data_list(self, data_list: List[MarketData]) -> List[MarketData]:
        """验证市场数据列表"""
        if not data_list:
            return data_list
        
        validated_data = []
        errors = []
        
        for i, data in enumerate(data_list):
            try:
                if self._validate_single_data(data):
                    validated_data.append(data)
                else:
                    errors.append(f"数据验证失败: 索引{i}")
            except Exception as e:
                errors.append(f"数据验证异常: 索引{i}, {e}")
        
        if errors:
            self.logger.warning(f"数据验证发现问题: {len(errors)}个错误")
            for error in errors[:5]:  # 只记录前5个错误
                self.logger.warning(error)
        
        self.logger.info(f"数据验证完成: {len(validated_data)}/{len(data_list)}条数据有效")
        return validated_data
    
    def _validate_single_data(self, data: MarketData) -> bool:
        """验证单条市场数据"""
        try:
            # 基本字段验证
            if not data.symbol or not data.timestamp:
                return False
            
            # 价格验证
            if data.high.value < data.low.value:
                return False
            
            if data.high.value < max(data.open.value, data.close.value):
                return False
            
            if data.low.value > min(data.open.value, data.close.value):
                return False
            
            # 成交量验证
            if data.volume.value < 0:
                return False
            
            # 时间验证
            if data.timestamp > datetime.now():
                return False
            
            return True
            
        except Exception:
            return False
    
    def check_data_continuity(self, data_list: List[MarketData]) -> Dict[str, Any]:
        """检查数据连续性"""
        if len(data_list) < 2:
            return {'is_continuous': True, 'gaps': []}
        
        # 按时间排序
        sorted_data = sorted(data_list, key=lambda x: x.timestamp)
        
        gaps = []
        expected_interval = self._get_expected_interval(sorted_data[0].timeframe)
        
        for i in range(1, len(sorted_data)):
            current_time = sorted_data[i].timestamp
            previous_time = sorted_data[i-1].timestamp
            actual_interval = (current_time - previous_time).total_seconds()
            
            if actual_interval > expected_interval * 1.5:  # 允许50%的误差
                gaps.append({
                    'start': previous_time,
                    'end': current_time,
                    'duration': actual_interval
                })
        
        return {
            'is_continuous': len(gaps) == 0,
            'gaps': gaps,
            'total_gaps': len(gaps)
        }
    
    def _get_expected_interval(self, timeframe: TimeFrame) -> float:
        """获取预期的时间间隔（秒）"""
        intervals = {
            TimeFrame.M1: 60,
            TimeFrame.M5: 300,
            TimeFrame.M15: 900,
            TimeFrame.M30: 1800,
            TimeFrame.H1: 3600,
            TimeFrame.H4: 14400,
            TimeFrame.D1: 86400,
            TimeFrame.W1: 604800
        }
        return intervals.get(timeframe, 86400)


class DataManager:
    """
    数据管理器
    
    数据管理核心模块 - 封装所有数据复杂性，提供统一的数据获取接口。
    """
    
    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("data_manager")
        
        # 初始化组件
        cache_size = config.get_config("system.performance.cache_size", 1000)
        cache_ttl = config.get_config("system.cache.ttl", 3600)
        self._cache = DataCache(cache_size, cache_ttl)
        self._validator = DataValidator()
        
        # 初始化数据源
        self._data_sources: Dict[str, IDataSource] = {}
        self._initialize_data_sources()
        
        # 实时数据订阅
        self._realtime_subscriptions: Dict[str, List[Callable]] = {}
        self._subscription_tasks: Dict[str, asyncio.Task] = {}
    
    def _initialize_data_sources(self) -> None:
        """初始化数据源"""
        try:
            # Binance数据源
            if self.config.get_config("data_sources.binance.enabled", False):
                self._data_sources["binance"] = BinanceDataSource(self.config)
                self.logger.info("Binance数据源已初始化")
            
            # Yahoo Finance数据源
            if self.config.get_config("data_sources.yahoo_finance.enabled", False):
                self._data_sources["yahoo_finance"] = YahooFinanceDataSource(self.config)
                self.logger.info("Yahoo Finance数据源已初始化")
            
            # 本地文件数据源
            if self.config.get_config("data_sources.local_file.enabled", False):
                self._data_sources["local_file"] = LocalFileDataSource(self.config)
                self.logger.info("本地文件数据源已初始化")
            
            if not self._data_sources:
                self.logger.warning("没有可用的数据源")
            
        except Exception as e:
            self.logger.error(f"初始化数据源失败: {e}")
            raise DataSourceException(f"初始化数据源失败: {e}")
    
    async def get_market_data(self, request: DataRequest) -> MarketDataSet:
        """
        统一数据获取接口
        
        Args:
            request: 数据请求对象
            
        Returns:
            市场数据集
        """
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(request)
            
            # 检查缓存
            if request.use_cache:
                cached_data = self._cache.get(cache_key)
                if cached_data:
                    self.logger.debug(f"从缓存获取数据: {request.symbol}")
                    return cached_data
            
            # 选择数据源
            data_source = self._select_data_source(request)
            if not data_source:
                raise DataSourceException(f"没有可用的数据源: {request.symbol}")
            
            # 获取数据
            self.logger.info(f"从{data_source.name}获取数据: {request.symbol}")
            raw_data = await data_source.get_historical_data(
                request.symbol,
                request.timeframe,
                request.start_date,
                request.end_date
            )
            
            # 验证数据
            if request.validate_data:
                validated_data = self._validator.validate_market_data_list(raw_data)
            else:
                validated_data = raw_data
            
            # 创建数据集
            data_set = MarketDataSet(
                symbol=request.symbol,
                timeframe=request.timeframe,
                data=validated_data,
                source=data_source.name,
                timestamp=datetime.now(),
                metadata={
                    'original_count': len(raw_data),
                    'validated_count': len(validated_data),
                    'start_date': request.start_date,
                    'end_date': request.end_date
                }
            )
            
            # 缓存数据
            if request.use_cache:
                self._cache.set(cache_key, data_set)
            
            self.logger.info(f"数据获取成功: {request.symbol}, {len(validated_data)}条记录")
            return data_set
            
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {request.symbol}, {e}")
            raise DataSourceException(f"获取市场数据失败: {e}")
    
    async def get_realtime_data(self, symbol: str, source: Optional[str] = None) -> MarketData:
        """获取实时数据"""
        try:
            data_source = self._get_data_source(source)
            if not data_source:
                raise DataSourceException(f"数据源不可用: {source}")
            
            return await data_source.get_realtime_data(symbol)
            
        except Exception as e:
            self.logger.error(f"获取实时数据失败: {symbol}, {e}")
            raise DataSourceException(f"获取实时数据失败: {e}")
    
    async def subscribe_realtime_data(
        self, 
        symbols: List[str], 
        callback: Callable[[MarketData], None],
        source: Optional[str] = None
    ) -> str:
        """订阅实时数据"""
        try:
            data_source = self._get_data_source(source)
            if not data_source:
                raise DataSourceException(f"数据源不可用: {source}")
            
            subscription_id = f"{data_source.name}_{hash(tuple(symbols))}"
            
            # 创建订阅任务
            task = asyncio.create_task(
                data_source.subscribe_realtime_feed(symbols, callback)
            )
            
            self._subscription_tasks[subscription_id] = task
            self.logger.info(f"实时数据订阅成功: {symbols}, 订阅ID: {subscription_id}")
            
            return subscription_id
            
        except Exception as e:
            self.logger.error(f"订阅实时数据失败: {symbols}, {e}")
            raise DataSourceException(f"订阅实时数据失败: {e}")
    
    def unsubscribe_realtime_data(self, subscription_id: str) -> bool:
        """取消实时数据订阅"""
        try:
            if subscription_id in self._subscription_tasks:
                task = self._subscription_tasks[subscription_id]
                task.cancel()
                del self._subscription_tasks[subscription_id]
                self.logger.info(f"实时数据订阅已取消: {subscription_id}")
                return True
            else:
                self.logger.warning(f"订阅ID不存在: {subscription_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"取消订阅失败: {subscription_id}, {e}")
            return False
    
    def _select_data_source(self, request: DataRequest) -> Optional[IDataSource]:
        """选择数据源"""
        if request.source and request.source in self._data_sources:
            return self._data_sources[request.source]
        
        # 根据品种类型选择默认数据源
        if request.symbol.endswith('USDT') or request.symbol.endswith('BTC'):
            return self._data_sources.get("binance")
        else:
            return self._data_sources.get("yahoo_finance") or self._data_sources.get("local_file")
    
    def _get_data_source(self, source: Optional[str]) -> Optional[IDataSource]:
        """获取指定数据源"""
        if source:
            return self._data_sources.get(source)
        
        # 返回第一个可用的数据源
        return next(iter(self._data_sources.values()), None)
    
    def _generate_cache_key(self, request: DataRequest) -> str:
        """生成缓存键"""
        return CacheUtils.generate_cache_key(
            request.symbol,
            request.timeframe.value,
            request.start_date.isoformat(),
            request.end_date.isoformat(),
            request.source
        )
    
    def get_available_symbols(self, source: Optional[str] = None) -> List[str]:
        """获取可用的交易品种"""
        if source and source in self._data_sources:
            return asyncio.run(self._data_sources[source].get_available_symbols())
        
        # 合并所有数据源的品种
        all_symbols = set()
        for data_source in self._data_sources.values():
            try:
                symbols = asyncio.run(data_source.get_available_symbols())
                all_symbols.update(symbols)
            except Exception as e:
                self.logger.warning(f"获取{data_source.name}品种列表失败: {e}")
        
        return list(all_symbols)
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return self._cache.get_cache_info()
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._cache.clear()
    
    async def cleanup(self) -> None:
        """清理资源"""
        # 取消所有订阅
        for subscription_id in list(self._subscription_tasks.keys()):
            self.unsubscribe_realtime_data(subscription_id)
        
        # 清理数据源
        for data_source in self._data_sources.values():
            if hasattr(data_source, 'cleanup'):
                try:
                    await data_source.cleanup()
                except Exception as e:
                    self.logger.warning(f"清理数据源失败: {e}")
        
        self.logger.info("数据管理器已清理")
