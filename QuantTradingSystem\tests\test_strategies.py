"""
策略测试模块

测试各种策略的功能。
"""

import unittest
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal

from domain.value_objects import MarketData, Price, Volume
from shared.constants import TimeFrame, AssetType
from application.strategies import SimpleMovingAverageCrossStrategy, RSIStrategy, StrategyContext


class TestSimpleMovingAverageCrossStrategy(unittest.TestCase):
    """测试简单移动平均交叉策略"""
    
    def setUp(self):
        """设置测试环境"""
        self.strategy_config = {
            'fast_period': 5,
            'slow_period': 10,
            'signal_threshold': 0.01
        }
        self.strategy = SimpleMovingAverageCrossStrategy('test_sma', self.strategy_config)
        
        # 创建测试上下文
        self.context = StrategyContext(
            strategy_id='test_sma',
            config=self.strategy_config
        )
    
    async def test_strategy_initialization(self):
        """测试策略初始化"""
        await self.strategy.initialize(self.context)
        self.assertEqual(self.strategy.fast_period, 5)
        self.assertEqual(self.strategy.slow_period, 10)
        self.assertEqual(self.strategy.signal_threshold, 0.01)
    
    async def test_signal_generation(self):
        """测试信号生成"""
        await self.strategy.initialize(self.context)
        
        # 创建测试数据
        base_time = datetime.now()
        test_prices = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112]
        
        signals = []
        for i, price in enumerate(test_prices):
            market_data = MarketData(
                symbol='TEST',
                timestamp=base_time + timedelta(minutes=i),
                open=Price(Decimal(str(price - 0.5))),
                high=Price(Decimal(str(price + 0.5))),
                low=Price(Decimal(str(price - 1))),
                close=Price(Decimal(str(price))),
                volume=Volume(Decimal('1000')),
                timeframe=TimeFrame.M1,
                asset_type=AssetType.STOCK
            )
            
            strategy_signals = await self.strategy.on_data(market_data)
            signals.extend(strategy_signals)
        
        # 验证信号生成
        self.assertGreater(len(signals), 0, "应该生成至少一个信号")
    
    def test_parameter_validation(self):
        """测试参数验证"""
        # 有效参数
        valid_params = {
            'fast_period': 5,
            'slow_period': 20,
            'signal_threshold': 0.02
        }
        self.assertTrue(self.strategy.validate_parameters(valid_params))
        
        # 无效参数 - fast_period >= slow_period
        invalid_params = {
            'fast_period': 20,
            'slow_period': 10,
            'signal_threshold': 0.02
        }
        self.assertFalse(self.strategy.validate_parameters(invalid_params))
        
        # 无效参数 - signal_threshold 超出范围
        invalid_params2 = {
            'fast_period': 5,
            'slow_period': 20,
            'signal_threshold': 1.5
        }
        self.assertFalse(self.strategy.validate_parameters(invalid_params2))


class TestRSIStrategy(unittest.TestCase):
    """测试RSI策略"""
    
    def setUp(self):
        """设置测试环境"""
        self.strategy_config = {
            'period': 14,
            'oversold_threshold': 30,
            'overbought_threshold': 70
        }
        self.strategy = RSIStrategy('test_rsi', self.strategy_config)
        
        # 创建测试上下文
        self.context = StrategyContext(
            strategy_id='test_rsi',
            config=self.strategy_config
        )
    
    async def test_strategy_initialization(self):
        """测试策略初始化"""
        await self.strategy.initialize(self.context)
        self.assertEqual(self.strategy.period, 14)
        self.assertEqual(self.strategy.oversold_threshold, 30)
        self.assertEqual(self.strategy.overbought_threshold, 70)
    
    async def test_rsi_calculation(self):
        """测试RSI计算"""
        await self.strategy.initialize(self.context)
        
        # 创建测试数据 - 模拟价格上涨趋势
        base_time = datetime.now()
        test_prices = [100 + i * 0.5 for i in range(20)]  # 逐渐上涨的价格
        
        for i, price in enumerate(test_prices):
            market_data = MarketData(
                symbol='TEST',
                timestamp=base_time + timedelta(minutes=i),
                open=Price(Decimal(str(price - 0.1))),
                high=Price(Decimal(str(price + 0.1))),
                low=Price(Decimal(str(price - 0.2))),
                close=Price(Decimal(str(price))),
                volume=Volume(Decimal('1000')),
                timeframe=TimeFrame.M1,
                asset_type=AssetType.STOCK
            )
            
            signals = await self.strategy.on_data(market_data)
            
            # 在有足够数据后，应该能计算RSI
            if i >= self.strategy.period:
                # 由于是上涨趋势，RSI应该较高，可能生成卖出信号
                pass
    
    def test_parameter_validation(self):
        """测试参数验证"""
        # 有效参数
        valid_params = {
            'period': 14,
            'oversold_threshold': 30,
            'overbought_threshold': 70
        }
        self.assertTrue(self.strategy.validate_parameters(valid_params))
        
        # 无效参数 - oversold >= overbought
        invalid_params = {
            'period': 14,
            'oversold_threshold': 70,
            'overbought_threshold': 30
        }
        self.assertFalse(self.strategy.validate_parameters(invalid_params))


class AsyncTestCase(unittest.TestCase):
    """异步测试基类"""
    
    def setUp(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        self.loop.close()
    
    def async_test(self, coro):
        """运行异步测试"""
        return self.loop.run_until_complete(coro)


class TestStrategiesAsync(AsyncTestCase):
    """异步策略测试"""
    
    def test_sma_strategy_async(self):
        """异步测试SMA策略"""
        strategy_config = {
            'fast_period': 3,
            'slow_period': 5,
            'signal_threshold': 0.01
        }
        strategy = SimpleMovingAverageCrossStrategy('test_sma_async', strategy_config)
        
        context = StrategyContext(
            strategy_id='test_sma_async',
            config=strategy_config
        )
        
        async def run_test():
            await strategy.initialize(context)
            
            # 创建测试数据
            base_time = datetime.now()
            test_prices = [100, 101, 99, 102, 98, 103, 97, 104]
            
            all_signals = []
            for i, price in enumerate(test_prices):
                market_data = MarketData(
                    symbol='TEST_ASYNC',
                    timestamp=base_time + timedelta(minutes=i),
                    open=Price(Decimal(str(price - 0.5))),
                    high=Price(Decimal(str(price + 0.5))),
                    low=Price(Decimal(str(price - 1))),
                    close=Price(Decimal(str(price))),
                    volume=Volume(Decimal('1000')),
                    timeframe=TimeFrame.M1,
                    asset_type=AssetType.STOCK
                )
                
                signals = await strategy.on_data(market_data)
                all_signals.extend(signals)
            
            return all_signals
        
        signals = self.async_test(run_test())
        
        # 验证策略状态
        self.assertIsNotNone(strategy.metrics)
        self.assertGreaterEqual(strategy.metrics.total_signals, 0)
    
    def test_rsi_strategy_async(self):
        """异步测试RSI策略"""
        strategy_config = {
            'period': 5,  # 较短的周期用于测试
            'oversold_threshold': 30,
            'overbought_threshold': 70
        }
        strategy = RSIStrategy('test_rsi_async', strategy_config)
        
        context = StrategyContext(
            strategy_id='test_rsi_async',
            config=strategy_config
        )
        
        async def run_test():
            await strategy.initialize(context)
            
            # 创建测试数据 - 先下跌后上涨
            base_time = datetime.now()
            test_prices = [100, 95, 90, 85, 80, 85, 90, 95, 100, 105]
            
            all_signals = []
            for i, price in enumerate(test_prices):
                market_data = MarketData(
                    symbol='TEST_RSI_ASYNC',
                    timestamp=base_time + timedelta(minutes=i),
                    open=Price(Decimal(str(price - 0.5))),
                    high=Price(Decimal(str(price + 0.5))),
                    low=Price(Decimal(str(price - 1))),
                    close=Price(Decimal(str(price))),
                    volume=Volume(Decimal('1000')),
                    timeframe=TimeFrame.M1,
                    asset_type=AssetType.STOCK
                )
                
                signals = await strategy.on_data(market_data)
                all_signals.extend(signals)
            
            return all_signals
        
        signals = self.async_test(run_test())
        
        # 验证策略状态
        self.assertIsNotNone(strategy.metrics)
        self.assertGreaterEqual(strategy.metrics.total_signals, 0)


if __name__ == '__main__':
    unittest.main()
