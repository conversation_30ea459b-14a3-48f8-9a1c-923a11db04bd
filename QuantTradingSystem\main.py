#!/usr/bin/env python3
"""
量化交易系统主入口模块

提供系统的启动、初始化和主要控制流程。
"""

import argparse
import asyncio
import logging
import os
import sys
from pathlib import Path

# 确保Python版本兼容
if sys.version_info < (3, 13):
    print("错误: 需要Python 3.13或更高版本")
    sys.exit(1)

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from QuantTradingSystem.infrastructure.config import ConfigurationManager
from QuantTradingSystem.application.services import DataService, StrategyService
from QuantTradingSystem.application.orchestrators import TradingOrchestrator
from QuantTradingSystem.presentation.gui import MainWindow
from QuantTradingSystem.shared.utils import Logger


def setup_logging(config):
    """设置日志系统"""
    log_config = config.get_config("system.logging")
    log_level = getattr(logging, log_config.get("level", "INFO"))
    log_path = log_config.get("file_path", "logs/system.log")
    
    # 确保日志目录存在
    os.makedirs(os.path.dirname(log_path), exist_ok=True)
    
    # 初始化日志系统
    Logger.initialize(
        level=log_level,
        file_path=log_path,
        format=log_config.get("format"),
        max_file_size=log_config.get("max_file_size"),
        backup_count=log_config.get("backup_count", 5)
    )
    
    return Logger.get_logger("main")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="量化交易系统")
    
    parser.add_argument(
        "--config-dir", 
        type=str, 
        default="config",
        help="配置文件目录路径"
    )
    
    parser.add_argument(
        "--mode", 
        type=str, 
        choices=["gui", "cli", "backtest", "optimize"], 
        default="gui",
        help="运行模式: gui=图形界面, cli=命令行, backtest=回测, optimize=优化"
    )
    
    parser.add_argument(
        "--strategy", 
        type=str, 
        help="要运行的策略名称"
    )
    
    parser.add_argument(
        "--symbol", 
        type=str, 
        help="交易品种代码"
    )
    
    parser.add_argument(
        "--paper-trading", 
        action="store_true",
        help="使用模拟交易模式"
    )
    
    parser.add_argument(
        "--debug", 
        action="store_true",
        help="启用调试模式"
    )
    
    return parser.parse_args()


async def run_cli_mode(config, args, logger):
    """运行命令行模式"""
    logger.info("启动命令行模式")
    
    # 初始化服务
    data_service = DataService(config)
    strategy_service = StrategyService(config)
    
    # 初始化交易协调器
    orchestrator = TradingOrchestrator(
        data_service=data_service,
        strategy_service=strategy_service,
        config=config
    )
    
    # 加载策略
    if args.strategy:
        await strategy_service.load_strategy(args.strategy)
    
    # 启动交易
    if args.symbol:
        await orchestrator.start_trading(
            symbols=[args.symbol],
            paper_trading=args.paper_trading
        )
    else:
        logger.error("未指定交易品种，无法启动交易")
        return
    
    # 等待用户中断
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("用户中断，正在停止交易...")
        await orchestrator.stop_trading()


def run_gui_mode(config, args, logger):
    """运行图形界面模式"""
    logger.info("启动图形界面模式")
    
    # 创建并启动主窗口
    app = MainWindow(config)
    app.run()


async def run_backtest_mode(config, args, logger):
    """运行回测模式"""
    from QuantTradingSystem.application.services import BacktestService
    
    logger.info("启动回测模式")
    
    if not args.strategy or not args.symbol:
        logger.error("回测模式需要指定策略和交易品种")
        return
    
    # 初始化回测服务
    backtest_service = BacktestService(config)
    
    # 运行回测
    result = await backtest_service.run_backtest(
        strategy_name=args.strategy,
        symbol=args.symbol
    )
    
    # 输出回测结果
    logger.info(f"回测结果: {result}")


async def run_optimize_mode(config, args, logger):
    """运行优化模式"""
    from QuantTradingSystem.application.services import BacktestService
    
    logger.info("启动优化模式")
    
    if not args.strategy or not args.symbol:
        logger.error("优化模式需要指定策略和交易品种")
        return
    
    # 初始化回测服务
    backtest_service = BacktestService(config)
    
    # 运行优化
    result = await backtest_service.optimize_parameters(
        strategy_name=args.strategy,
        symbol=args.symbol
    )
    
    # 输出优化结果
    logger.info(f"优化结果: {result}")


async def async_main():
    """异步主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 初始化配置管理器
    config = ConfigurationManager(args.config_dir)
    
    # 设置日志
    logger = setup_logging(config)
    logger.info("量化交易系统启动中...")
    
    # 设置调试模式
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("调试模式已启用")
    
    # 根据运行模式执行相应功能
    try:
        if args.mode == "gui":
            run_gui_mode(config, args, logger)
        elif args.mode == "cli":
            await run_cli_mode(config, args, logger)
        elif args.mode == "backtest":
            await run_backtest_mode(config, args, logger)
        elif args.mode == "optimize":
            await run_optimize_mode(config, args, logger)
    except Exception as e:
        logger.exception(f"运行时错误: {e}")
        return 1
    
    logger.info("量化交易系统正常退出")
    return 0


def main():
    """主函数"""
    try:
        # 检查是否支持自由线程模式
        if hasattr(sys, "_is_gil_enabled") and not sys._is_gil_enabled():
            print("检测到Python 3.13自由线程模式，将使用并行处理能力")
        
        # 运行异步主函数
        exit_code = asyncio.run(async_main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断，程序退出")
        sys.exit(0)


if __name__ == "__main__":
    main()
