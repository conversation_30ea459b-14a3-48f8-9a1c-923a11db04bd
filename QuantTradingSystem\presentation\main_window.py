"""
主窗口模块

应用程序主窗口 - 整合所有UI组件。
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import asyncio
import threading
from typing import Optional

from shared.utils import Logger
from infrastructure.config import ConfigurationManager
from presentation.controllers import MainController
from presentation.gui import AsyncTkinter, StatusBar, StrategyPanel, BacktestPanel


class MainWindow:
    """
    主窗口类
    
    应用程序的主要用户界面。
    """
    
    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("main_window")
        
        # 初始化控制器
        self.controller = MainController(config)
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("量化交易系统")
        self.root.geometry("1200x800")
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 异步Tkinter包装器
        self.async_tk = AsyncTkinter(self.root)
        
        # 设置控制器引用
        self.controller.set_main_window(self)
        self.controller.set_async_tk(self.async_tk)
        
        # 创建UI组件
        self._create_widgets()
        self._setup_layout()
        self._setup_menu()
        self._setup_bindings()
        
        # 启动控制器
        self._startup()
    
    def _create_widgets(self):
        """创建UI组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 策略管理标签页
        self.strategy_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.strategy_frame, text="策略管理")
        self.strategy_panel = StrategyPanel(self.strategy_frame, self.controller)
        
        # 回测管理标签页
        self.backtest_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.backtest_frame, text="回测管理")
        self.backtest_panel = BacktestPanel(self.backtest_frame, self.controller)
        
        # 投资组合标签页
        self.portfolio_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.portfolio_frame, text="投资组合")
        self.portfolio_panel = self._create_portfolio_panel()
        
        # 实时交易标签页
        self.trading_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.trading_frame, text="实时交易")
        self.trading_panel = self._create_trading_panel()
        
        # 系统监控标签页
        self.monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.monitor_frame, text="系统监控")
        self.monitor_panel = self._create_monitor_panel()
        
        # 状态栏
        self.status_bar = StatusBar(self.root)
    
    def _create_portfolio_panel(self):
        """创建投资组合面板"""
        panel = ttk.LabelFrame(self.portfolio_frame, text="投资组合管理", padding=10)
        
        # 投资组合列表
        portfolio_tree = ttk.Treeview(panel, columns=('value', 'cash', 'positions', 'return'), 
                                    show='tree headings')
        portfolio_tree.heading('#0', text='组合名称')
        portfolio_tree.heading('value', text='总价值')
        portfolio_tree.heading('cash', text='现金')
        portfolio_tree.heading('positions', text='持仓数')
        portfolio_tree.heading('return', text='收益率')
        
        # 滚动条
        portfolio_scrollbar = ttk.Scrollbar(panel, orient=tk.VERTICAL, 
                                          command=portfolio_tree.yview)
        portfolio_tree.configure(yscrollcommand=portfolio_scrollbar.set)
        
        # 按钮框架
        button_frame = ttk.Frame(panel)
        
        # 按钮
        create_button = ttk.Button(button_frame, text="创建组合", 
                                 command=self._create_portfolio)
        delete_button = ttk.Button(button_frame, text="删除组合", 
                                 command=self._delete_portfolio)
        refresh_button = ttk.Button(button_frame, text="刷新", 
                                  command=self._refresh_portfolios)
        
        # 布局
        portfolio_tree.grid(row=0, column=0, sticky='nsew', padx=(0, 5))
        portfolio_scrollbar.grid(row=0, column=1, sticky='ns')
        button_frame.grid(row=1, column=0, columnspan=2, sticky='ew', pady=(10, 0))
        
        create_button.pack(side=tk.LEFT, padx=(0, 5))
        delete_button.pack(side=tk.LEFT, padx=(0, 5))
        refresh_button.pack(side=tk.LEFT)
        
        panel.grid_rowconfigure(0, weight=1)
        panel.grid_columnconfigure(0, weight=1)
        
        # 保存引用
        panel.portfolio_tree = portfolio_tree
        
        return panel
    
    def _create_trading_panel(self):
        """创建实时交易面板"""
        panel = ttk.LabelFrame(self.trading_frame, text="实时交易", padding=10)
        
        # 交易会话框架
        session_frame = ttk.LabelFrame(panel, text="交易会话", padding=5)
        
        # 会话参数
        ttk.Label(session_frame, text="投资组合:").grid(row=0, column=0, sticky='w', padx=(0, 5))
        portfolio_combo = ttk.Combobox(session_frame, width=20)
        portfolio_combo.grid(row=0, column=1, sticky='ew', padx=(0, 10))
        
        ttk.Label(session_frame, text="交易品种:").grid(row=0, column=2, sticky='w', padx=(0, 5))
        symbols_entry = ttk.Entry(session_frame, width=30)
        symbols_entry.grid(row=0, column=3, sticky='ew')
        
        ttk.Label(session_frame, text="策略:").grid(row=1, column=0, sticky='w', padx=(0, 5), pady=(5, 0))
        strategies_entry = ttk.Entry(session_frame, width=20)
        strategies_entry.grid(row=1, column=1, sticky='ew', padx=(0, 10), pady=(5, 0))
        
        # 模拟交易选项
        paper_trading_var = tk.BooleanVar(value=True)
        paper_trading_check = ttk.Checkbutton(session_frame, text="模拟交易", 
                                            variable=paper_trading_var)
        paper_trading_check.grid(row=1, column=2, sticky='w', padx=(0, 5), pady=(5, 0))
        
        # 按钮
        start_session_button = ttk.Button(session_frame, text="启动会话", 
                                        command=self._start_trading_session)
        start_session_button.grid(row=1, column=3, sticky='ew', pady=(5, 0))
        
        # 活跃会话列表
        sessions_frame = ttk.LabelFrame(panel, text="活跃会话", padding=5)
        
        sessions_tree = ttk.Treeview(sessions_frame, columns=('portfolio', 'symbols', 'strategies', 'status'), 
                                   show='tree headings')
        sessions_tree.heading('#0', text='会话ID')
        sessions_tree.heading('portfolio', text='投资组合')
        sessions_tree.heading('symbols', text='品种')
        sessions_tree.heading('strategies', text='策略')
        sessions_tree.heading('status', text='状态')
        
        sessions_scrollbar = ttk.Scrollbar(sessions_frame, orient=tk.VERTICAL, 
                                         command=sessions_tree.yview)
        sessions_tree.configure(yscrollcommand=sessions_scrollbar.set)
        
        # 会话控制按钮
        session_button_frame = ttk.Frame(sessions_frame)
        stop_session_button = ttk.Button(session_button_frame, text="停止会话", 
                                       command=self._stop_trading_session)
        refresh_sessions_button = ttk.Button(session_button_frame, text="刷新", 
                                           command=self._refresh_sessions)
        
        # 布局
        session_frame.grid(row=0, column=0, sticky='ew', padx=(0, 0), pady=(0, 10))
        session_frame.grid_columnconfigure(1, weight=1)
        session_frame.grid_columnconfigure(3, weight=1)
        
        sessions_frame.grid(row=1, column=0, sticky='nsew')
        sessions_tree.grid(row=0, column=0, sticky='nsew', padx=(0, 5))
        sessions_scrollbar.grid(row=0, column=1, sticky='ns')
        session_button_frame.grid(row=1, column=0, columnspan=2, sticky='ew', pady=(10, 0))
        
        stop_session_button.pack(side=tk.LEFT, padx=(0, 5))
        refresh_sessions_button.pack(side=tk.LEFT)
        
        sessions_frame.grid_rowconfigure(0, weight=1)
        sessions_frame.grid_columnconfigure(0, weight=1)
        
        # 保存引用
        panel.portfolio_combo = portfolio_combo
        panel.symbols_entry = symbols_entry
        panel.strategies_entry = strategies_entry
        panel.paper_trading_var = paper_trading_var
        panel.sessions_tree = sessions_tree
        
        return panel
    
    def _create_monitor_panel(self):
        """创建系统监控面板"""
        panel = ttk.LabelFrame(self.monitor_frame, text="系统监控", padding=10)
        
        # 系统状态
        status_frame = ttk.LabelFrame(panel, text="系统状态", padding=5)
        
        status_text = tk.Text(status_frame, height=10, wrap=tk.WORD)
        status_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, 
                                       command=status_text.yview)
        status_text.configure(yscrollcommand=status_scrollbar.set)
        
        # 信号处理统计
        stats_frame = ttk.LabelFrame(panel, text="信号处理统计", padding=5)
        
        stats_text = tk.Text(stats_frame, height=8, wrap=tk.WORD)
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, 
                                      command=stats_text.yview)
        stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        # 控制按钮
        button_frame = ttk.Frame(panel)
        refresh_status_button = ttk.Button(button_frame, text="刷新状态", 
                                         command=self._refresh_system_status)
        clear_cache_button = ttk.Button(button_frame, text="清空缓存", 
                                      command=self._clear_cache)
        
        # 布局
        status_frame.grid(row=0, column=0, sticky='nsew', pady=(0, 10))
        status_text.grid(row=0, column=0, sticky='nsew', padx=(0, 5))
        status_scrollbar.grid(row=0, column=1, sticky='ns')
        status_frame.grid_rowconfigure(0, weight=1)
        status_frame.grid_columnconfigure(0, weight=1)
        
        stats_frame.grid(row=1, column=0, sticky='nsew', pady=(0, 10))
        stats_text.grid(row=0, column=0, sticky='nsew', padx=(0, 5))
        stats_scrollbar.grid(row=0, column=1, sticky='ns')
        stats_frame.grid_rowconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(0, weight=1)
        
        button_frame.grid(row=2, column=0, sticky='ew')
        refresh_status_button.pack(side=tk.LEFT, padx=(0, 5))
        clear_cache_button.pack(side=tk.LEFT)
        
        # 保存引用
        panel.status_text = status_text
        panel.stats_text = stats_text
        
        return panel
    
    def _setup_layout(self):
        """设置布局"""
        # 主框架
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 笔记本控件
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 各个面板
        self.strategy_panel.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.backtest_panel.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.portfolio_panel.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.trading_panel.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.monitor_panel.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 状态栏
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 配置权重
        self.portfolio_panel.grid_rowconfigure(1, weight=1)
        self.portfolio_panel.grid_columnconfigure(0, weight=1)
        
        self.trading_panel.grid_rowconfigure(1, weight=1)
        self.trading_panel.grid_columnconfigure(0, weight=1)
        
        self.monitor_panel.grid_rowconfigure(0, weight=1)
        self.monitor_panel.grid_rowconfigure(1, weight=1)
        self.monitor_panel.grid_columnconfigure(0, weight=1)
    
    def _setup_menu(self):
        """设置菜单"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="保存配置", command=self._save_config)
        file_menu.add_command(label="加载配置", command=self._load_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清空缓存", command=self._clear_cache)
        tools_menu.add_command(label="重新加载配置", command=self._reload_config)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _setup_bindings(self):
        """设置事件绑定"""
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 快捷键
        self.root.bind('<Control-s>', lambda e: self._save_config())
        self.root.bind('<Control-o>', lambda e: self._load_config())
        self.root.bind('<F5>', lambda e: self._refresh_all())
    
    def _startup(self):
        """启动应用"""
        def startup_async():
            try:
                future = self.async_tk.run_async(self.controller.start())
                # 等待启动完成
                future.result(timeout=30)
                
                # 在主线程中更新状态
                self.root.after_idle(lambda: self.status_bar.set_status("系统已启动"))
                self.root.after_idle(self._refresh_all)
                
            except Exception as e:
                self.logger.error(f"系统启动失败: {e}")
                self.root.after_idle(lambda: messagebox.showerror("错误", f"系统启动失败: {str(e)}"))
        
        # 在后台线程中启动
        threading.Thread(target=startup_async, daemon=True).start()
        self.status_bar.set_status("系统启动中...")
    
    def run(self):
        """运行应用"""
        try:
            self.root.mainloop()
        finally:
            self._cleanup()
    
    def _cleanup(self):
        """清理资源"""
        try:
            # 停止异步循环
            self.async_tk.stop()
            
            # 停止控制器
            future = self.async_tk.run_async(self.controller.stop())
            future.result(timeout=10)
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
    
    def _on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出量化交易系统吗？"):
            self.root.quit()
    
    # 事件处理方法
    def _save_config(self):
        """保存配置"""
        try:
            filename = filedialog.asksaveasfilename(
                title="保存配置",
                defaultextension=".yaml",
                filetypes=[("YAML files", "*.yaml"), ("All files", "*.*")]
            )
            if filename:
                self.controller.save_config(filename)
                messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def _load_config(self):
        """加载配置"""
        try:
            filename = filedialog.askopenfilename(
                title="加载配置",
                filetypes=[("YAML files", "*.yaml"), ("All files", "*.*")]
            )
            if filename:
                # 这里需要实现配置加载逻辑
                messagebox.showinfo("提示", "配置加载功能待实现")
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {str(e)}")
    
    def _clear_cache(self):
        """清空缓存"""
        try:
            self.controller.clear_cache()
            messagebox.showinfo("成功", "缓存已清空")
        except Exception as e:
            messagebox.showerror("错误", f"清空缓存失败: {str(e)}")
    
    def _reload_config(self):
        """重新加载配置"""
        try:
            self.controller.reload_config()
            messagebox.showinfo("成功", "配置已重新加载")
        except Exception as e:
            messagebox.showerror("错误", f"重新加载配置失败: {str(e)}")
    
    def _show_about(self):
        """显示关于对话框"""
        messagebox.showinfo("关于", "量化交易系统 v1.0\n\n基于Python 3.13开发的量化交易平台")
    
    def _refresh_all(self):
        """刷新所有面板"""
        self.strategy_panel.refresh_strategies()
        self._refresh_portfolios()
        self._refresh_sessions()
        self._refresh_system_status()
    
    # 投资组合相关方法
    def _create_portfolio(self):
        """创建投资组合"""
        # 简化的创建对话框
        name = tk.simpledialog.askstring("创建投资组合", "请输入组合名称:")
        if name:
            capital = tk.simpledialog.askfloat("创建投资组合", "请输入初始资金:", initialvalue=100000.0)
            if capital:
                try:
                    self.controller.create_portfolio(name, capital)
                    messagebox.showinfo("成功", "投资组合创建成功")
                    self._refresh_portfolios()
                except Exception as e:
                    messagebox.showerror("错误", f"创建投资组合失败: {str(e)}")
    
    def _delete_portfolio(self):
        """删除投资组合"""
        selected = self.portfolio_panel.portfolio_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要删除的投资组合")
            return
        
        portfolio_name = self.portfolio_panel.portfolio_tree.item(selected[0])['text']
        if messagebox.askyesno("确认", f"确定要删除投资组合 '{portfolio_name}' 吗？"):
            try:
                # 这里需要获取portfolio_id，简化处理
                messagebox.showinfo("提示", "删除功能待完善")
            except Exception as e:
                messagebox.showerror("错误", f"删除投资组合失败: {str(e)}")
    
    def _refresh_portfolios(self):
        """刷新投资组合列表"""
        try:
            # 清空现有项目
            for item in self.portfolio_panel.portfolio_tree.get_children():
                self.portfolio_panel.portfolio_tree.delete(item)
            
            # 获取投资组合信息
            portfolios = self.controller.get_portfolios()
            for portfolio_id, portfolio_info in portfolios.items():
                if portfolio_info:
                    name = portfolio_info.get('name', 'Unknown')
                    total_value = portfolio_info.get('total_value', 0)
                    cash_balance = portfolio_info.get('cash_balance', 0)
                    position_count = portfolio_info.get('position_count', 0)
                    performance = portfolio_info.get('performance', {})
                    total_return = performance.get('total_return', 0)
                    
                    self.portfolio_panel.portfolio_tree.insert('', 'end', text=name,
                                                             values=(f"${total_value:.2f}",
                                                                   f"${cash_balance:.2f}",
                                                                   position_count,
                                                                   f"{total_return:.2%}"))
        except Exception as e:
            self.logger.error(f"刷新投资组合失败: {e}")
    
    # 交易会话相关方法
    def _start_trading_session(self):
        """启动交易会话"""
        # 简化实现
        messagebox.showinfo("提示", "交易会话功能待实现")
    
    def _stop_trading_session(self):
        """停止交易会话"""
        messagebox.showinfo("提示", "停止会话功能待实现")
    
    def _refresh_sessions(self):
        """刷新交易会话"""
        try:
            # 清空现有项目
            for item in self.trading_panel.sessions_tree.get_children():
                self.trading_panel.sessions_tree.delete(item)
            
            # 获取活跃会话
            sessions = self.controller.get_active_sessions()
            for session_id, session_info in sessions.items():
                portfolio_id = session_info.get('portfolio_id', '')
                symbols = ', '.join(session_info.get('symbols', []))
                strategies = ', '.join(session_info.get('strategies', []))
                status = "活跃" if session_info.get('is_active', False) else "停止"
                
                self.trading_panel.sessions_tree.insert('', 'end', text=session_id,
                                                       values=(portfolio_id, symbols, strategies, status))
        except Exception as e:
            self.logger.error(f"刷新交易会话失败: {e}")
    
    # 系统监控相关方法
    def _refresh_system_status(self):
        """刷新系统状态"""
        try:
            # 获取系统状态
            status = self.controller.get_system_status()
            
            # 显示系统状态
            self.monitor_panel.status_text.delete(1.0, tk.END)
            self.monitor_panel.status_text.insert(tk.END, "=== 系统状态 ===\n")
            for service_name, service_status in status.items():
                if isinstance(service_status, dict):
                    is_running = service_status.get('is_running', False)
                    error_count = service_status.get('error_count', 0)
                    self.monitor_panel.status_text.insert(tk.END, 
                        f"{service_name}: {'运行中' if is_running else '已停止'} (错误: {error_count})\n")
                else:
                    self.monitor_panel.status_text.insert(tk.END, f"{service_name}: {service_status}\n")
            
            # 获取信号处理统计
            stats = self.controller.get_signal_processing_stats()
            
            # 显示统计信息
            self.monitor_panel.stats_text.delete(1.0, tk.END)
            self.monitor_panel.stats_text.insert(tk.END, "=== 信号处理统计 ===\n")
            for key, value in stats.items():
                self.monitor_panel.stats_text.insert(tk.END, f"{key}: {value}\n")
            
        except Exception as e:
            self.logger.error(f"刷新系统状态失败: {e}")


# 简化的对话框
import tkinter.simpledialog
