"""
领域服务模块

定义领域层的服务，封装复杂的业务逻辑和跨实体的操作。
这些服务不属于任何特定实体，但包含重要的业务规则。
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any, Tuple
import statistics
import math

from .entities import Portfolio, Position, Transaction, Account
from .value_objects import MarketData, Signal, Order, Price, Volume, PerformanceMetrics
from shared.constants import SignalType, PositionSide, OrderSide
from shared.exceptions import (
    RiskManagementException, PositionSizeException, 
    DrawdownException, ValidationException
)


class PortfolioService:
    """投资组合服务"""
    
    @staticmethod
    def calculate_position_size(
        portfolio: Portfolio,
        signal: Signal,
        risk_per_trade: float = 0.02,
        max_position_size: float = 0.25
    ) -> Volume:
        """
        计算仓位大小
        
        Args:
            portfolio: 投资组合
            signal: 交易信号
            risk_per_trade: 每笔交易风险比例
            max_position_size: 最大仓位比例
            
        Returns:
            建议的仓位大小
        """
        if not signal.price or not signal.stop_loss:
            # 如果没有价格或止损信息，使用固定比例
            max_value = portfolio.total_value.value * Decimal(str(max_position_size))
            if signal.price:
                quantity = max_value / signal.price.value
            else:
                quantity = Decimal('0')
            return Volume(quantity)
        
        # 基于风险的仓位计算
        risk_amount = portfolio.total_value.value * Decimal(str(risk_per_trade))
        price_diff = abs(signal.price.value - signal.stop_loss.value)
        
        if price_diff == 0:
            return Volume(Decimal('0'))
        
        # 计算基于风险的数量
        risk_based_quantity = risk_amount / price_diff
        
        # 计算基于最大仓位的数量
        max_value = portfolio.total_value.value * Decimal(str(max_position_size))
        max_quantity = max_value / signal.price.value
        
        # 取较小值
        final_quantity = min(risk_based_quantity, max_quantity)
        
        return Volume(max(final_quantity, Decimal('0')))
    
    @staticmethod
    def validate_new_position(
        portfolio: Portfolio,
        symbol: str,
        quantity: Volume,
        price: Price,
        max_positions: int = 10,
        max_correlation: float = 0.7
    ) -> bool:
        """
        验证新仓位是否符合风险管理规则
        
        Args:
            portfolio: 投资组合
            symbol: 交易品种
            quantity: 数量
            price: 价格
            max_positions: 最大持仓数量
            max_correlation: 最大相关性
            
        Returns:
            是否可以开仓
        """
        # 检查最大持仓数量
        open_positions = portfolio.get_open_positions()
        if len(open_positions) >= max_positions:
            return False
        
        # 检查是否已有该品种的持仓
        existing_position = portfolio.get_position(symbol)
        if existing_position and existing_position.is_open:
            return False
        
        # 检查仓位大小
        position_value = price.value * quantity.value
        portfolio_value = portfolio.total_value.value
        
        if portfolio_value > 0:
            position_ratio = float(position_value / portfolio_value)
            if position_ratio > 0.25:  # 单个仓位不超过25%
                return False
        
        # 检查资金充足性
        required_cash = position_value
        if required_cash > portfolio.account.available_balance.value:
            return False
        
        return True
    
    @staticmethod
    def rebalance_portfolio(
        portfolio: Portfolio,
        target_weights: Dict[str, float]
    ) -> List[Order]:
        """
        重新平衡投资组合
        
        Args:
            portfolio: 投资组合
            target_weights: 目标权重
            
        Returns:
            需要执行的订单列表
        """
        orders = []
        total_value = portfolio.total_value.value
        
        if total_value == 0:
            return orders
        
        # 计算当前权重
        current_weights = {}
        for symbol, position in portfolio.positions.items():
            if position.is_open:
                weight = float(position.market_value.value / total_value)
                current_weights[symbol] = weight
        
        # 计算需要调整的权重
        for symbol, target_weight in target_weights.items():
            current_weight = current_weights.get(symbol, 0.0)
            weight_diff = target_weight - current_weight
            
            if abs(weight_diff) > 0.01:  # 权重差异超过1%才调整
                target_value = total_value * Decimal(str(target_weight))
                current_position = portfolio.get_position(symbol)
                
                if current_position and current_position.is_open:
                    current_value = current_position.market_value.value
                    value_diff = target_value - current_value
                    
                    if value_diff > 0:
                        # 需要买入
                        quantity = value_diff / current_position.current_price.value
                        order = Order(
                            symbol=symbol,
                            side=OrderSide.BUY,
                            quantity=Volume(quantity),
                            price=current_position.current_price
                        )
                        orders.append(order)
                    elif value_diff < 0:
                        # 需要卖出
                        quantity = abs(value_diff) / current_position.current_price.value
                        quantity = min(quantity, current_position.quantity.value)
                        order = Order(
                            symbol=symbol,
                            side=OrderSide.SELL,
                            quantity=Volume(quantity),
                            price=current_position.current_price
                        )
                        orders.append(order)
        
        return orders


class RiskCalculationService:
    """风险计算服务"""
    
    @staticmethod
    def calculate_portfolio_var(
        portfolio: Portfolio,
        confidence_level: float = 0.95,
        time_horizon: int = 1
    ) -> Price:
        """
        计算投资组合的风险价值(VaR)
        
        Args:
            portfolio: 投资组合
            confidence_level: 置信水平
            time_horizon: 时间范围（天）
            
        Returns:
            VaR值
        """
        # 简化的VaR计算，实际应该使用历史数据或蒙特卡洛模拟
        total_value = portfolio.total_value.value
        
        # 假设日波动率为2%
        daily_volatility = 0.02
        
        # 计算VaR
        from scipy import stats
        z_score = stats.norm.ppf(1 - confidence_level)
        var_value = total_value * daily_volatility * math.sqrt(time_horizon) * abs(z_score)
        
        return Price(Decimal(str(var_value)), portfolio.account.current_balance.currency)
    
    @staticmethod
    def calculate_position_risk(
        position: Position,
        market_data: List[MarketData]
    ) -> Dict[str, float]:
        """
        计算单个持仓的风险指标
        
        Args:
            position: 持仓
            market_data: 历史市场数据
            
        Returns:
            风险指标字典
        """
        if not market_data or len(market_data) < 2:
            return {}
        
        # 计算价格变化
        price_changes = []
        for i in range(1, len(market_data)):
            change = (market_data[i].close.value - market_data[i-1].close.value) / market_data[i-1].close.value
            price_changes.append(float(change))
        
        if not price_changes:
            return {}
        
        # 计算风险指标
        volatility = statistics.stdev(price_changes) if len(price_changes) > 1 else 0.0
        
        # 计算最大回撤
        prices = [float(data.close.value) for data in market_data]
        max_drawdown = 0.0
        peak = prices[0]
        
        for price in prices:
            if price > peak:
                peak = price
            else:
                drawdown = (peak - price) / peak
                max_drawdown = max(max_drawdown, drawdown)
        
        return {
            'volatility': volatility,
            'max_drawdown': max_drawdown,
            'current_drawdown': (float(position.average_price.value) - float(position.current_price.value)) / float(position.average_price.value) if position.side == PositionSide.LONG else 0.0
        }
    
    @staticmethod
    def check_risk_limits(
        portfolio: Portfolio,
        max_drawdown: float = 0.2,
        max_var_ratio: float = 0.1
    ) -> List[str]:
        """
        检查风险限制
        
        Args:
            portfolio: 投资组合
            max_drawdown: 最大回撤限制
            max_var_ratio: 最大VaR比例限制
            
        Returns:
            违反的风险限制列表
        """
        violations = []
        
        # 检查回撤
        current_value = portfolio.total_value.value
        initial_value = portfolio.account.initial_balance.value
        
        if initial_value > 0:
            drawdown = float((initial_value - current_value) / initial_value)
            if drawdown > max_drawdown:
                violations.append(f"回撤超限: {drawdown:.2%} > {max_drawdown:.2%}")
        
        # 检查VaR
        var_value = RiskCalculationService.calculate_portfolio_var(portfolio)
        if current_value > 0:
            var_ratio = float(var_value.value / current_value)
            if var_ratio > max_var_ratio:
                violations.append(f"VaR超限: {var_ratio:.2%} > {max_var_ratio:.2%}")
        
        return violations


class PerformanceCalculationService:
    """性能计算服务"""
    
    @staticmethod
    def calculate_portfolio_performance(
        portfolio: Portfolio,
        benchmark_returns: Optional[List[float]] = None
    ) -> PerformanceMetrics:
        """
        计算投资组合性能指标
        
        Args:
            portfolio: 投资组合
            benchmark_returns: 基准收益率列表
            
        Returns:
            性能指标
        """
        if not portfolio.transactions:
            return PerformanceMetrics()
        
        # 计算基本指标
        initial_value = float(portfolio.account.initial_balance.value)
        current_value = float(portfolio.total_value.value)
        
        if initial_value == 0:
            return PerformanceMetrics()
        
        total_return = (current_value - initial_value) / initial_value
        
        # 计算交易收益率序列
        returns = PerformanceCalculationService._calculate_returns(portfolio)
        
        # 计算各项指标
        annualized_return = PerformanceCalculationService._calculate_annualized_return(
            total_return, len(portfolio.transactions)
        )
        
        volatility = statistics.stdev(returns) if len(returns) > 1 else 0.0
        sharpe_ratio = PerformanceCalculationService._calculate_sharpe_ratio(returns)
        sortino_ratio = PerformanceCalculationService._calculate_sortino_ratio(returns)
        max_drawdown = PerformanceCalculationService._calculate_max_drawdown(portfolio)
        calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0.0
        
        # 计算胜率
        winning_trades = sum(1 for r in returns if r > 0)
        win_rate = winning_trades / len(returns) if returns else 0.0
        
        # 计算盈亏比
        winning_returns = [r for r in returns if r > 0]
        losing_returns = [r for r in returns if r < 0]
        
        avg_win = statistics.mean(winning_returns) if winning_returns else 0.0
        avg_loss = abs(statistics.mean(losing_returns)) if losing_returns else 0.0
        profit_factor = avg_win / avg_loss if avg_loss > 0 else 0.0
        
        # 计算Alpha和Beta（如果有基准）
        alpha, beta = 0.0, 1.0
        if benchmark_returns and len(benchmark_returns) == len(returns):
            alpha, beta = PerformanceCalculationService._calculate_alpha_beta(
                returns, benchmark_returns
            )
        
        return PerformanceMetrics(
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            calmar_ratio=calmar_ratio,
            win_rate=win_rate,
            profit_factor=profit_factor,
            alpha=alpha,
            beta=beta
        )
    
    @staticmethod
    def _calculate_returns(portfolio: Portfolio) -> List[float]:
        """计算收益率序列"""
        returns = []
        prev_value = float(portfolio.account.initial_balance.value)
        
        # 简化版本：基于交易计算收益率
        for transaction in portfolio.transactions:
            # 这里需要更复杂的逻辑来计算每日收益率
            # 暂时使用简化版本
            pass
        
        return returns
    
    @staticmethod
    def _calculate_annualized_return(total_return: float, days: int) -> float:
        """计算年化收益率"""
        if days <= 0:
            return 0.0
        return (1 + total_return) ** (365.25 / days) - 1
    
    @staticmethod
    def _calculate_sharpe_ratio(returns: List[float], risk_free_rate: float = 0.0) -> float:
        """计算夏普比率"""
        if not returns or len(returns) < 2:
            return 0.0
        
        excess_returns = [r - risk_free_rate for r in returns]
        mean_excess = statistics.mean(excess_returns)
        std_excess = statistics.stdev(excess_returns)
        
        return mean_excess / std_excess if std_excess > 0 else 0.0
    
    @staticmethod
    def _calculate_sortino_ratio(returns: List[float], risk_free_rate: float = 0.0) -> float:
        """计算索提诺比率"""
        if not returns:
            return 0.0
        
        excess_returns = [r - risk_free_rate for r in returns]
        mean_excess = statistics.mean(excess_returns)
        
        # 只考虑负收益的标准差
        negative_returns = [r for r in excess_returns if r < 0]
        if not negative_returns:
            return float('inf') if mean_excess > 0 else 0.0
        
        downside_deviation = statistics.stdev(negative_returns)
        return mean_excess / downside_deviation if downside_deviation > 0 else 0.0
    
    @staticmethod
    def _calculate_max_drawdown(portfolio: Portfolio) -> float:
        """计算最大回撤"""
        # 简化版本，实际需要基于历史净值计算
        initial_value = float(portfolio.account.initial_balance.value)
        current_value = float(portfolio.total_value.value)
        
        if initial_value == 0:
            return 0.0
        
        return max(0.0, (initial_value - current_value) / initial_value)
    
    @staticmethod
    def _calculate_alpha_beta(
        portfolio_returns: List[float], 
        benchmark_returns: List[float]
    ) -> Tuple[float, float]:
        """计算Alpha和Beta"""
        if len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) < 2:
            return 0.0, 1.0
        
        # 计算协方差和方差
        portfolio_mean = statistics.mean(portfolio_returns)
        benchmark_mean = statistics.mean(benchmark_returns)
        
        covariance = statistics.covariance(portfolio_returns, benchmark_returns)
        benchmark_variance = statistics.variance(benchmark_returns)
        
        if benchmark_variance == 0:
            return 0.0, 1.0
        
        beta = covariance / benchmark_variance
        alpha = portfolio_mean - beta * benchmark_mean
        
        return alpha, beta


class SignalValidationService:
    """信号验证服务"""
    
    @staticmethod
    def validate_signal(signal: Signal, market_data: Optional[MarketData] = None) -> bool:
        """
        验证交易信号
        
        Args:
            signal: 交易信号
            market_data: 当前市场数据
            
        Returns:
            信号是否有效
        """
        # 基本验证
        if not signal.is_valid():
            return False
        
        # 时间验证
        if signal.timestamp > datetime.utcnow():
            return False
        
        # 价格验证
        if market_data and signal.price:
            price_diff = abs(signal.price.value - market_data.close.value) / market_data.close.value
            if price_diff > 0.05:  # 价格偏差超过5%
                return False
        
        # 信号强度验证
        if signal.strength < 0.3:  # 信号强度太低
            return False
        
        return True
    
    @staticmethod
    def filter_conflicting_signals(signals: List[Signal]) -> List[Signal]:
        """
        过滤冲突的信号
        
        Args:
            signals: 信号列表
            
        Returns:
            过滤后的信号列表
        """
        if not signals:
            return []
        
        # 按品种分组
        symbol_signals = {}
        for signal in signals:
            if signal.symbol not in symbol_signals:
                symbol_signals[signal.symbol] = []
            symbol_signals[signal.symbol].append(signal)
        
        filtered_signals = []
        
        for symbol, symbol_signal_list in symbol_signals.items():
            # 按时间排序
            symbol_signal_list.sort(key=lambda s: s.timestamp)
            
            # 只保留最新的信号
            if symbol_signal_list:
                latest_signal = symbol_signal_list[-1]
                if latest_signal.is_actionable():
                    filtered_signals.append(latest_signal)
        
        return filtered_signals
