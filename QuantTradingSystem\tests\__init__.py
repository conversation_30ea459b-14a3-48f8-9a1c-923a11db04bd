"""
测试模块

包含系统的各种测试用例。
"""

# 测试配置
TEST_CONFIG = {
    'data_sources': {
        'local_file': {
            'enabled': True,
            'data_directory': 'tests/data'
        }
    },
    'strategies': {
        'default': {
            'sma_cross': {
                'enabled': True,
                'fast_period': 5,
                'slow_period': 10,
                'signal_threshold': 0.01
            }
        }
    }
}
