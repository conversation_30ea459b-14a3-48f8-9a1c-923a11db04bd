"""
策略模块

定义策略抽象接口和具体策略实现。
策略定义与执行分离，支持热加载和动态配置。
"""

import asyncio
import importlib
import inspect
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional, Type, Callable
from dataclasses import dataclass, field
from pathlib import Path

from domain.value_objects import MarketData, Signal
from domain.entities import Portfolio, Position
from shared.constants import SignalType, StrategyStatus
from shared.exceptions import StrategyException, StrategyLoadException, StrategyExecutionException
from shared.utils import Logger, PerformanceUtils
from infrastructure.config import ConfigurationManager


@dataclass
class StrategyContext:
    """策略上下文"""
    strategy_id: str
    config: Dict[str, Any]
    portfolio: Optional[Portfolio] = None
    current_data: Optional[MarketData] = None
    historical_data: List[MarketData] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StrategyMetrics:
    """策略指标"""
    total_signals: int = 0
    successful_signals: int = 0
    failed_signals: int = 0
    avg_execution_time: float = 0.0
    last_execution_time: Optional[datetime] = None
    error_count: int = 0
    last_error: Optional[str] = None


class IStrategy(ABC):
    """
    策略统一接口 - 通用性设计
    
    所有策略必须实现此接口，确保策略的一致性和可替换性。
    """
    
    def __init__(self, strategy_id: str, config: Dict[str, Any]):
        self.strategy_id = strategy_id
        self.config = config
        self.logger = Logger.get_logger(f"strategy.{strategy_id}")
        self.status = StrategyStatus.INACTIVE
        self.metrics = StrategyMetrics()
        self._context: Optional[StrategyContext] = None
    
    @abstractmethod
    async def initialize(self, context: StrategyContext) -> None:
        """
        策略初始化
        
        Args:
            context: 策略上下文
        """
        pass
    
    @abstractmethod
    async def on_data(self, data: MarketData) -> List[Signal]:
        """
        数据处理和信号生成
        
        Args:
            data: 市场数据
            
        Returns:
            生成的信号列表
        """
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取策略参数
        
        Returns:
            策略参数字典
        """
        pass
    
    @abstractmethod
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """
        验证策略参数
        
        Args:
            parameters: 参数字典
            
        Returns:
            参数是否有效
        """
        pass
    
    async def update_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        更新策略参数
        
        Args:
            parameters: 新参数
        """
        if self.validate_parameters(parameters):
            self.config.update(parameters)
            self.logger.info(f"策略参数已更新: {parameters}")
        else:
            raise StrategyException(f"无效的策略参数: {parameters}", strategy_name=self.strategy_id)
    
    async def start(self) -> None:
        """启动策略"""
        self.status = StrategyStatus.ACTIVE
        self.logger.info(f"策略已启动: {self.strategy_id}")
    
    async def stop(self) -> None:
        """停止策略"""
        self.status = StrategyStatus.INACTIVE
        self.logger.info(f"策略已停止: {self.strategy_id}")
    
    async def pause(self) -> None:
        """暂停策略"""
        self.status = StrategyStatus.PAUSED
        self.logger.info(f"策略已暂停: {self.strategy_id}")
    
    async def resume(self) -> None:
        """恢复策略"""
        self.status = StrategyStatus.ACTIVE
        self.logger.info(f"策略已恢复: {self.strategy_id}")
    
    def is_active(self) -> bool:
        """检查策略是否活跃"""
        return self.status == StrategyStatus.ACTIVE
    
    def get_metrics(self) -> StrategyMetrics:
        """获取策略指标"""
        return self.metrics
    
    def update_context(self, context: StrategyContext) -> None:
        """更新策略上下文"""
        self._context = context
    
    async def cleanup(self) -> None:
        """清理资源"""
        self.logger.info(f"策略清理完成: {self.strategy_id}")


class SimpleMovingAverageCrossStrategy(IStrategy):
    """简单移动平均交叉策略"""
    
    def __init__(self, strategy_id: str, config: Dict[str, Any]):
        super().__init__(strategy_id, config)
        
        # 策略参数
        self.fast_period = config.get("fast_period", 10)
        self.slow_period = config.get("slow_period", 30)
        self.signal_threshold = config.get("signal_threshold", 0.01)
        
        # 内部状态
        self._price_history: List[float] = []
        self._last_signal_type: Optional[SignalType] = None
    
    async def initialize(self, context: StrategyContext) -> None:
        """初始化策略"""
        self.update_context(context)
        self._price_history.clear()
        self._last_signal_type = None
        
        self.logger.info(f"SMA交叉策略初始化完成: fast={self.fast_period}, slow={self.slow_period}")
    
    async def on_data(self, data: MarketData) -> List[Signal]:
        """处理数据并生成信号"""
        try:
            # 更新价格历史
            self._price_history.append(float(data.close.value))
            
            # 保持历史数据长度
            max_history = max(self.slow_period * 2, 100)
            if len(self._price_history) > max_history:
                self._price_history = self._price_history[-max_history:]
            
            # 检查是否有足够的数据
            if len(self._price_history) < self.slow_period:
                return []
            
            # 计算移动平均线
            fast_ma = self._calculate_sma(self.fast_period)
            slow_ma = self._calculate_sma(self.slow_period)
            
            if fast_ma is None or slow_ma is None:
                return []
            
            # 生成信号
            signals = []
            signal_type = self._determine_signal_type(fast_ma, slow_ma)
            
            if signal_type and signal_type != self._last_signal_type:
                signal_strength = self._calculate_signal_strength(fast_ma, slow_ma)
                
                if signal_strength >= self.signal_threshold:
                    signal = Signal(
                        strategy_id=self.strategy_id,
                        symbol=data.symbol,
                        signal_type=signal_type,
                        strength=signal_strength,
                        timestamp=data.timestamp,
                        price=data.close,
                        metadata={
                            'fast_ma': fast_ma,
                            'slow_ma': slow_ma,
                            'fast_period': self.fast_period,
                            'slow_period': self.slow_period
                        }
                    )
                    
                    signals.append(signal)
                    self._last_signal_type = signal_type
                    
                    self.logger.info(f"生成信号: {signal_type.value}, 强度: {signal_strength:.3f}")
            
            # 更新指标
            self.metrics.total_signals += len(signals)
            self.metrics.last_execution_time = datetime.now()
            
            return signals
            
        except Exception as e:
            self.metrics.error_count += 1
            self.metrics.last_error = str(e)
            self.logger.error(f"策略执行失败: {e}")
            raise StrategyExecutionException(f"策略执行失败: {e}", strategy_name=self.strategy_id)
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数"""
        return {
            'fast_period': self.fast_period,
            'slow_period': self.slow_period,
            'signal_threshold': self.signal_threshold
        }
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """验证策略参数"""
        try:
            fast_period = parameters.get('fast_period', self.fast_period)
            slow_period = parameters.get('slow_period', self.slow_period)
            signal_threshold = parameters.get('signal_threshold', self.signal_threshold)
            
            # 验证参数范围
            if not (1 <= fast_period <= 100):
                return False
            if not (1 <= slow_period <= 200):
                return False
            if not (0 <= signal_threshold <= 1):
                return False
            if fast_period >= slow_period:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _calculate_sma(self, period: int) -> Optional[float]:
        """计算简单移动平均"""
        if len(self._price_history) < period:
            return None
        
        return sum(self._price_history[-period:]) / period
    
    def _determine_signal_type(self, fast_ma: float, slow_ma: float) -> Optional[SignalType]:
        """确定信号类型"""
        if len(self._price_history) < self.slow_period + 1:
            return None
        
        # 计算前一个周期的移动平均
        prev_fast_ma = self._calculate_sma_at_offset(self.fast_period, 1)
        prev_slow_ma = self._calculate_sma_at_offset(self.slow_period, 1)
        
        if prev_fast_ma is None or prev_slow_ma is None:
            return None
        
        # 检查交叉
        if prev_fast_ma <= prev_slow_ma and fast_ma > slow_ma:
            return SignalType.BUY  # 金叉
        elif prev_fast_ma >= prev_slow_ma and fast_ma < slow_ma:
            return SignalType.SELL  # 死叉
        
        return None
    
    def _calculate_sma_at_offset(self, period: int, offset: int) -> Optional[float]:
        """计算指定偏移位置的移动平均"""
        if len(self._price_history) < period + offset:
            return None
        
        end_idx = len(self._price_history) - offset
        start_idx = end_idx - period
        
        return sum(self._price_history[start_idx:end_idx]) / period
    
    def _calculate_signal_strength(self, fast_ma: float, slow_ma: float) -> float:
        """计算信号强度"""
        if slow_ma == 0:
            return 0.0
        
        # 基于移动平均线差异计算强度
        diff_ratio = abs(fast_ma - slow_ma) / slow_ma
        
        # 归一化到0-1范围
        strength = min(diff_ratio / 0.05, 1.0)  # 5%差异对应最大强度
        
        return strength


class RSIStrategy(IStrategy):
    """相对强弱指数策略"""
    
    def __init__(self, strategy_id: str, config: Dict[str, Any]):
        super().__init__(strategy_id, config)
        
        self.period = config.get("period", 14)
        self.oversold_threshold = config.get("oversold_threshold", 30)
        self.overbought_threshold = config.get("overbought_threshold", 70)
        
        self._price_changes: List[float] = []
        self._last_price: Optional[float] = None
    
    async def initialize(self, context: StrategyContext) -> None:
        """初始化策略"""
        self.update_context(context)
        self._price_changes.clear()
        self._last_price = None
        
        self.logger.info(f"RSI策略初始化完成: period={self.period}")
    
    async def on_data(self, data: MarketData) -> List[Signal]:
        """处理数据并生成信号"""
        try:
            current_price = float(data.close.value)
            
            # 计算价格变化
            if self._last_price is not None:
                price_change = current_price - self._last_price
                self._price_changes.append(price_change)
                
                # 保持历史数据长度
                if len(self._price_changes) > self.period * 2:
                    self._price_changes = self._price_changes[-self.period * 2:]
            
            self._last_price = current_price
            
            # 检查是否有足够的数据
            if len(self._price_changes) < self.period:
                return []
            
            # 计算RSI
            rsi = self._calculate_rsi()
            if rsi is None:
                return []
            
            # 生成信号
            signals = []
            
            if rsi <= self.oversold_threshold:
                # 超卖，买入信号
                signal = Signal(
                    strategy_id=self.strategy_id,
                    symbol=data.symbol,
                    signal_type=SignalType.BUY,
                    strength=(self.oversold_threshold - rsi) / self.oversold_threshold,
                    timestamp=data.timestamp,
                    price=data.close,
                    metadata={'rsi': rsi, 'threshold': self.oversold_threshold}
                )
                signals.append(signal)
                
            elif rsi >= self.overbought_threshold:
                # 超买，卖出信号
                signal = Signal(
                    strategy_id=self.strategy_id,
                    symbol=data.symbol,
                    signal_type=SignalType.SELL,
                    strength=(rsi - self.overbought_threshold) / (100 - self.overbought_threshold),
                    timestamp=data.timestamp,
                    price=data.close,
                    metadata={'rsi': rsi, 'threshold': self.overbought_threshold}
                )
                signals.append(signal)
            
            self.metrics.total_signals += len(signals)
            self.metrics.last_execution_time = datetime.now()
            
            return signals
            
        except Exception as e:
            self.metrics.error_count += 1
            self.metrics.last_error = str(e)
            self.logger.error(f"RSI策略执行失败: {e}")
            raise StrategyExecutionException(f"RSI策略执行失败: {e}", strategy_name=self.strategy_id)
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数"""
        return {
            'period': self.period,
            'oversold_threshold': self.oversold_threshold,
            'overbought_threshold': self.overbought_threshold
        }
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """验证策略参数"""
        try:
            period = parameters.get('period', self.period)
            oversold = parameters.get('oversold_threshold', self.oversold_threshold)
            overbought = parameters.get('overbought_threshold', self.overbought_threshold)
            
            if not (1 <= period <= 50):
                return False
            if not (0 <= oversold <= 50):
                return False
            if not (50 <= overbought <= 100):
                return False
            if oversold >= overbought:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _calculate_rsi(self) -> Optional[float]:
        """计算RSI"""
        if len(self._price_changes) < self.period:
            return None
        
        recent_changes = self._price_changes[-self.period:]
        
        gains = [change for change in recent_changes if change > 0]
        losses = [-change for change in recent_changes if change < 0]
        
        avg_gain = sum(gains) / len(recent_changes) if gains else 0
        avg_loss = sum(losses) / len(recent_changes) if losses else 0
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi


# 策略注册表
STRATEGY_REGISTRY: Dict[str, Type[IStrategy]] = {
    'sma_cross': SimpleMovingAverageCrossStrategy,
    'rsi': RSIStrategy,
}
