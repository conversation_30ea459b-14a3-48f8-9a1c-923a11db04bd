"""
信号处理器模块

信号处理核心模块 - 封装信号过滤、验证、路由等复杂逻辑。
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass
from enum import Enum
import threading

from domain.value_objects import Signal, Order, Price, Volume
from domain.entities import Portfolio
from domain.services import SignalValidationService, PortfolioService
from shared.constants import SignalType, OrderType, OrderSide
from shared.exceptions import SignalException, SignalProcessingException
from shared.utils import Logger, AsyncUtils
from infrastructure.config import ConfigurationManager
from application.risk_manager import RiskManager


class SignalAction(Enum):
    """信号处理动作"""
    ACCEPT = "ACCEPT"
    REJECT = "REJECT"
    MODIFY = "MODIFY"
    DEFER = "DEFER"


@dataclass
class SignalProcessingResult:
    """信号处理结果"""
    action: SignalAction
    original_signal: Signal
    processed_signal: Optional[Signal] = None
    generated_orders: List[Order] = None
    reason: str = ""
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.generated_orders is None:
            self.generated_orders = []
        if self.metadata is None:
            self.metadata = {}


class ISignalFilter(ABC):
    """信号过滤器接口"""
    
    @abstractmethod
    async def filter_signal(self, signal: Signal, context: Dict[str, Any]) -> Tuple[bool, str]:
        """
        过滤信号
        
        Args:
            signal: 输入信号
            context: 上下文信息
            
        Returns:
            (是否通过过滤, 原因)
        """
        pass
    
    @abstractmethod
    def get_filter_name(self) -> str:
        """获取过滤器名称"""
        pass


class SignalStrengthFilter(ISignalFilter):
    """信号强度过滤器"""
    
    def __init__(self, min_strength: float = 0.5):
        self.min_strength = min_strength
        self.logger = Logger.get_logger("filter.strength")
    
    async def filter_signal(self, signal: Signal, context: Dict[str, Any]) -> Tuple[bool, str]:
        """过滤信号强度"""
        if signal.strength < self.min_strength:
            return False, f"信号强度不足: {signal.strength:.3f} < {self.min_strength:.3f}"
        return True, ""
    
    def get_filter_name(self) -> str:
        return "SignalStrengthFilter"


class SignalTimeFilter(ISignalFilter):
    """信号时间过滤器"""
    
    def __init__(self, max_age_seconds: int = 300):
        self.max_age_seconds = max_age_seconds
        self.logger = Logger.get_logger("filter.time")
    
    async def filter_signal(self, signal: Signal, context: Dict[str, Any]) -> Tuple[bool, str]:
        """过滤信号时效性"""
        age = (datetime.now() - signal.timestamp).total_seconds()
        if age > self.max_age_seconds:
            return False, f"信号过期: {age:.1f}秒 > {self.max_age_seconds}秒"
        return True, ""
    
    def get_filter_name(self) -> str:
        return "SignalTimeFilter"


class DuplicateSignalFilter(ISignalFilter):
    """重复信号过滤器"""
    
    def __init__(self, dedup_window_seconds: int = 60):
        self.dedup_window_seconds = dedup_window_seconds
        self.logger = Logger.get_logger("filter.duplicate")
        self._recent_signals: Dict[str, datetime] = {}
        self._lock = threading.RLock()
    
    async def filter_signal(self, signal: Signal, context: Dict[str, Any]) -> Tuple[bool, str]:
        """过滤重复信号"""
        signal_key = f"{signal.strategy_id}_{signal.symbol}_{signal.signal_type.value}"
        current_time = datetime.now()
        
        with self._lock:
            # 清理过期记录
            expired_keys = [
                key for key, timestamp in self._recent_signals.items()
                if (current_time - timestamp).total_seconds() > self.dedup_window_seconds
            ]
            for key in expired_keys:
                del self._recent_signals[key]
            
            # 检查重复
            if signal_key in self._recent_signals:
                last_time = self._recent_signals[signal_key]
                time_diff = (current_time - last_time).total_seconds()
                return False, f"重复信号: {time_diff:.1f}秒内已有相同信号"
            
            # 记录新信号
            self._recent_signals[signal_key] = current_time
        
        return True, ""
    
    def get_filter_name(self) -> str:
        return "DuplicateSignalFilter"


class MarketHoursFilter(ISignalFilter):
    """市场时间过滤器"""
    
    def __init__(self, market_open: str = "09:30", market_close: str = "16:00"):
        self.market_open = market_open
        self.market_close = market_close
        self.logger = Logger.get_logger("filter.market_hours")
    
    async def filter_signal(self, signal: Signal, context: Dict[str, Any]) -> Tuple[bool, str]:
        """过滤市场时间"""
        from shared.utils import DateTimeUtils
        
        if not DateTimeUtils.is_market_hours(signal.timestamp, self.market_open, self.market_close):
            return False, f"非交易时间: {signal.timestamp.strftime('%H:%M')}"
        
        return True, ""
    
    def get_filter_name(self) -> str:
        return "MarketHoursFilter"


class ISignalProcessor(ABC):
    """信号处理器接口"""
    
    @abstractmethod
    async def process_signal(
        self, 
        signal: Signal, 
        portfolio: Portfolio,
        context: Dict[str, Any]
    ) -> SignalProcessingResult:
        """处理信号"""
        pass
    
    @abstractmethod
    def get_processor_name(self) -> str:
        """获取处理器名称"""
        pass


class OrderGenerationProcessor(ISignalProcessor):
    """订单生成处理器"""
    
    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("processor.order_generation")
    
    async def process_signal(
        self, 
        signal: Signal, 
        portfolio: Portfolio,
        context: Dict[str, Any]
    ) -> SignalProcessingResult:
        """将信号转换为订单"""
        try:
            orders = []
            
            if signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                orders.extend(await self._generate_buy_orders(signal, portfolio))
            elif signal.signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                orders.extend(await self._generate_sell_orders(signal, portfolio))
            elif signal.signal_type == SignalType.EXIT:
                orders.extend(await self._generate_exit_orders(signal, portfolio))
            
            if orders:
                return SignalProcessingResult(
                    action=SignalAction.ACCEPT,
                    original_signal=signal,
                    generated_orders=orders,
                    reason="订单生成成功",
                    metadata={"order_count": len(orders)}
                )
            else:
                return SignalProcessingResult(
                    action=SignalAction.REJECT,
                    original_signal=signal,
                    reason="无法生成有效订单"
                )
        
        except Exception as e:
            self.logger.error(f"订单生成失败: {e}")
            return SignalProcessingResult(
                action=SignalAction.REJECT,
                original_signal=signal,
                reason=f"订单生成异常: {e}"
            )
    
    async def _generate_buy_orders(self, signal: Signal, portfolio: Portfolio) -> List[Order]:
        """生成买入订单"""
        orders = []
        
        if not signal.price:
            return orders
        
        # 计算仓位大小
        quantity = PortfolioService.calculate_position_size(portfolio, signal)
        
        if quantity.value <= 0:
            return orders
        
        # 创建主订单
        main_order = Order(
            symbol=signal.symbol,
            order_type=OrderType.MARKET,
            side=OrderSide.BUY,
            quantity=quantity,
            price=signal.price,
            strategy_id=signal.strategy_id,
            signal_id=signal.id,
            metadata={
                "signal_strength": signal.strength,
                "signal_type": signal.signal_type.value
            }
        )
        orders.append(main_order)
        
        # 生成止损订单
        if signal.stop_loss:
            stop_order = Order(
                symbol=signal.symbol,
                order_type=OrderType.STOP,
                side=OrderSide.SELL,
                quantity=quantity,
                stop_price=signal.stop_loss,
                strategy_id=signal.strategy_id,
                signal_id=signal.id,
                metadata={"order_purpose": "stop_loss"}
            )
            orders.append(stop_order)
        
        # 生成止盈订单
        if signal.take_profit:
            profit_order = Order(
                symbol=signal.symbol,
                order_type=OrderType.LIMIT,
                side=OrderSide.SELL,
                quantity=quantity,
                price=signal.take_profit,
                strategy_id=signal.strategy_id,
                signal_id=signal.id,
                metadata={"order_purpose": "take_profit"}
            )
            orders.append(profit_order)
        
        return orders
    
    async def _generate_sell_orders(self, signal: Signal, portfolio: Portfolio) -> List[Order]:
        """生成卖出订单"""
        orders = []
        
        # 检查是否有持仓
        position = portfolio.get_position(signal.symbol)
        if not position or not position.is_open:
            self.logger.warning(f"没有{signal.symbol}的持仓，无法卖出")
            return orders
        
        # 创建卖出订单
        sell_order = Order(
            symbol=signal.symbol,
            order_type=OrderType.MARKET,
            side=OrderSide.SELL,
            quantity=position.quantity,
            price=signal.price,
            strategy_id=signal.strategy_id,
            signal_id=signal.id,
            metadata={
                "signal_strength": signal.strength,
                "signal_type": signal.signal_type.value,
                "position_id": position.id
            }
        )
        orders.append(sell_order)
        
        return orders
    
    async def _generate_exit_orders(self, signal: Signal, portfolio: Portfolio) -> List[Order]:
        """生成平仓订单"""
        return await self._generate_sell_orders(signal, portfolio)
    
    def get_processor_name(self) -> str:
        return "OrderGenerationProcessor"


class SignalProcessor:
    """
    信号处理器
    
    信号处理核心模块 - 封装信号过滤、验证、路由等复杂逻辑。
    """
    
    def __init__(self, config: ConfigurationManager, risk_manager: RiskManager):
        self.config = config
        self.risk_manager = risk_manager
        self.logger = Logger.get_logger("signal_processor")
        
        # 初始化过滤器
        self.filters: List[ISignalFilter] = []
        self._initialize_filters()
        
        # 初始化处理器
        self.processors: List[ISignalProcessor] = []
        self._initialize_processors()
        
        # 处理统计
        self._processing_stats = {
            "total_processed": 0,
            "accepted": 0,
            "rejected": 0,
            "modified": 0,
            "deferred": 0
        }
        self._stats_lock = threading.RLock()
    
    def _initialize_filters(self) -> None:
        """初始化信号过滤器"""
        filter_config = self.config.get_config("strategies.execution.signal_filtering", {})
        
        # 信号强度过滤器
        if filter_config.get("enabled", True):
            min_strength = filter_config.get("min_signal_strength", 0.5)
            self.filters.append(SignalStrengthFilter(min_strength))
        
        # 时间过滤器
        max_age = filter_config.get("max_signal_age_seconds", 300)
        self.filters.append(SignalTimeFilter(max_age))
        
        # 重复信号过滤器
        dedup_window = filter_config.get("dedup_window_seconds", 60)
        self.filters.append(DuplicateSignalFilter(dedup_window))
        
        # 市场时间过滤器
        self.filters.append(MarketHoursFilter())
        
        self.logger.info(f"信号过滤器已初始化: {len(self.filters)}个过滤器")
    
    def _initialize_processors(self) -> None:
        """初始化信号处理器"""
        # 订单生成处理器
        self.processors.append(OrderGenerationProcessor(self.config))
        
        self.logger.info(f"信号处理器已初始化: {len(self.processors)}个处理器")
    
    async def process_signal(
        self, 
        signal: Signal, 
        portfolio: Portfolio,
        context: Optional[Dict[str, Any]] = None
    ) -> SignalProcessingResult:
        """
        处理信号
        
        Args:
            signal: 输入信号
            portfolio: 投资组合
            context: 上下文信息
            
        Returns:
            信号处理结果
        """
        if context is None:
            context = {}
        
        try:
            with self._stats_lock:
                self._processing_stats["total_processed"] += 1
            
            # 1. 基础验证
            if not SignalValidationService.validate_signal(signal):
                result = SignalProcessingResult(
                    action=SignalAction.REJECT,
                    original_signal=signal,
                    reason="信号基础验证失败"
                )
                await self._update_stats(result.action)
                return result
            
            # 2. 过滤器检查
            for filter_instance in self.filters:
                try:
                    passed, reason = await filter_instance.filter_signal(signal, context)
                    if not passed:
                        result = SignalProcessingResult(
                            action=SignalAction.REJECT,
                            original_signal=signal,
                            reason=f"{filter_instance.get_filter_name()}: {reason}"
                        )
                        await self._update_stats(result.action)
                        return result
                
                except Exception as e:
                    self.logger.error(f"过滤器执行失败 {filter_instance.get_filter_name()}: {e}")
                    result = SignalProcessingResult(
                        action=SignalAction.REJECT,
                        original_signal=signal,
                        reason=f"过滤器异常: {e}"
                    )
                    await self._update_stats(result.action)
                    return result
            
            # 3. 风险检查
            risk_passed, risk_violations = await self.risk_manager.check_signal_risk(signal, portfolio)
            if not risk_passed:
                result = SignalProcessingResult(
                    action=SignalAction.REJECT,
                    original_signal=signal,
                    reason=f"风险检查失败: {'; '.join(risk_violations)}"
                )
                await self._update_stats(result.action)
                return result
            
            # 4. 信号处理
            for processor in self.processors:
                try:
                    result = await processor.process_signal(signal, portfolio, context)
                    
                    # 如果处理成功，进行订单风险检查
                    if result.action == SignalAction.ACCEPT and result.generated_orders:
                        for order in result.generated_orders:
                            order_risk_passed, order_violations = await self.risk_manager.check_order_risk(order, portfolio)
                            if not order_risk_passed:
                                result = SignalProcessingResult(
                                    action=SignalAction.REJECT,
                                    original_signal=signal,
                                    reason=f"订单风险检查失败: {'; '.join(order_violations)}"
                                )
                                break
                    
                    await self._update_stats(result.action)
                    return result
                
                except Exception as e:
                    self.logger.error(f"处理器执行失败 {processor.get_processor_name()}: {e}")
                    continue
            
            # 如果所有处理器都失败
            result = SignalProcessingResult(
                action=SignalAction.REJECT,
                original_signal=signal,
                reason="所有处理器执行失败"
            )
            await self._update_stats(result.action)
            return result
        
        except Exception as e:
            self.logger.error(f"信号处理异常: {e}")
            result = SignalProcessingResult(
                action=SignalAction.REJECT,
                original_signal=signal,
                reason=f"处理异常: {e}"
            )
            await self._update_stats(result.action)
            return result
    
    async def process_signals_batch(
        self, 
        signals: List[Signal], 
        portfolio: Portfolio,
        context: Optional[Dict[str, Any]] = None
    ) -> List[SignalProcessingResult]:
        """批量处理信号"""
        results = []
        
        # 并发处理信号
        tasks = [
            self.process_signal(signal, portfolio, context)
            for signal in signals
        ]
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    results[i] = SignalProcessingResult(
                        action=SignalAction.REJECT,
                        original_signal=signals[i],
                        reason=f"批量处理异常: {result}"
                    )
        
        except Exception as e:
            self.logger.error(f"批量信号处理失败: {e}")
            # 返回所有拒绝结果
            results = [
                SignalProcessingResult(
                    action=SignalAction.REJECT,
                    original_signal=signal,
                    reason=f"批量处理失败: {e}"
                )
                for signal in signals
            ]
        
        return results
    
    async def _update_stats(self, action: SignalAction) -> None:
        """更新处理统计"""
        with self._stats_lock:
            if action == SignalAction.ACCEPT:
                self._processing_stats["accepted"] += 1
            elif action == SignalAction.REJECT:
                self._processing_stats["rejected"] += 1
            elif action == SignalAction.MODIFY:
                self._processing_stats["modified"] += 1
            elif action == SignalAction.DEFER:
                self._processing_stats["deferred"] += 1
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计"""
        with self._stats_lock:
            stats = self._processing_stats.copy()
            
            # 计算比例
            total = stats["total_processed"]
            if total > 0:
                stats["acceptance_rate"] = stats["accepted"] / total
                stats["rejection_rate"] = stats["rejected"] / total
            else:
                stats["acceptance_rate"] = 0.0
                stats["rejection_rate"] = 0.0
            
            return stats
    
    def reset_stats(self) -> None:
        """重置统计"""
        with self._stats_lock:
            self._processing_stats = {
                "total_processed": 0,
                "accepted": 0,
                "rejected": 0,
                "modified": 0,
                "deferred": 0
            }
    
    def add_filter(self, filter_instance: ISignalFilter) -> None:
        """添加过滤器"""
        self.filters.append(filter_instance)
        self.logger.info(f"已添加过滤器: {filter_instance.get_filter_name()}")
    
    def remove_filter(self, filter_name: str) -> bool:
        """移除过滤器"""
        for i, filter_instance in enumerate(self.filters):
            if filter_instance.get_filter_name() == filter_name:
                del self.filters[i]
                self.logger.info(f"已移除过滤器: {filter_name}")
                return True
        return False
    
    def add_processor(self, processor: ISignalProcessor) -> None:
        """添加处理器"""
        self.processors.append(processor)
        self.logger.info(f"已添加处理器: {processor.get_processor_name()}")
    
    def remove_processor(self, processor_name: str) -> bool:
        """移除处理器"""
        for i, processor in enumerate(self.processors):
            if processor.get_processor_name() == processor_name:
                del self.processors[i]
                self.logger.info(f"已移除处理器: {processor_name}")
                return True
        return False
