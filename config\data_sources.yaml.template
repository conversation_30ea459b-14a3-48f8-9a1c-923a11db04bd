# 数据源配置模板
# 复制此文件为 data_sources.yaml 并根据需要修改配置

data_sources:
  # Binance 配置
  binance:
    enabled: true
    api_key: "${BINANCE_API_KEY}"
    secret_key: "${BINANCE_SECRET_KEY}"
    base_url: "https://api.binance.com"
    testnet_url: "https://testnet.binance.vision"
    use_testnet: true
    rate_limit: 1200  # 每分钟请求限制
    timeout: 30
    retry_attempts: 3
    
  # Alpha Vantage 配置
  alpha_vantage:
    enabled: false
    api_key: "${ALPHA_VANTAGE_API_KEY}"
    base_url: "https://www.alphavantage.co"
    rate_limit: 5  # 每分钟请求限制
    timeout: 30
    
  # Yahoo Finance 配置
  yahoo_finance:
    enabled: true
    base_url: "https://query1.finance.yahoo.com"
    timeout: 30
    retry_attempts: 3
    
  # 本地文件数据源
  local_file:
    enabled: true
    data_directory: "data/market_data"
    supported_formats:
      - csv
      - json
      - parquet
    
  # 数据缓存配置
  cache:
    enabled: true
    cache_directory: "data/cache"
    max_cache_size: "1GB"
    cache_expiry: 3600  # 秒
    
  # 数据验证配置
  validation:
    enabled: true
    check_duplicates: true
    check_missing_values: true
    check_outliers: true
    outlier_threshold: 3.0  # 标准差倍数
    
  # 实时数据配置
  realtime:
    enabled: true
    websocket_timeout: 60
    reconnect_attempts: 5
    reconnect_delay: 5  # 秒
    buffer_size: 1000
    
  # 历史数据配置
  historical:
    default_timeframe: "1d"
    max_history_days: 365
    batch_size: 1000
    
  # 数据质量配置
  quality:
    min_data_points: 100
    max_gap_ratio: 0.05  # 最大缺失数据比例
    price_change_threshold: 0.2  # 价格变化阈值
