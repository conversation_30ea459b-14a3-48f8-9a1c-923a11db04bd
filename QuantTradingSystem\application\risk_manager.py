"""
风险管理器模块

风险管理核心模块 - 封装复杂的风险控制逻辑，提供统一的风险管理接口。
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import threading

from domain.value_objects import Signal, Order, Price, Volume, MarketData
from domain.entities import Portfolio, Position
from domain.services import RiskCalculationService
from shared.constants import SignalType, OrderSide, RiskLevel
from shared.exceptions import (
    RiskManagementException, PositionSizeException,
    DrawdownException, VaRException
)
from shared.utils import Logger, MathUtils
from infrastructure.config import ConfigurationManager


@dataclass
class RiskLimits:
    """风险限制配置"""
    max_position_size: float = 0.25  # 最大单个仓位比例
    max_total_exposure: float = 0.8   # 最大总敞口比例
    max_drawdown: float = 0.2         # 最大回撤比例
    max_daily_loss: float = 0.05      # 最大日损失比例
    max_var_ratio: float = 0.1        # 最大VaR比例
    max_correlation: float = 0.7      # 最大相关性
    max_positions: int = 10           # 最大持仓数量
    stop_loss_threshold: float = 0.02 # 止损阈值
    take_profit_ratio: float = 2.0    # 止盈比例（相对止损）


@dataclass
class RiskMetrics:
    """风险指标"""
    current_drawdown: float = 0.0
    daily_pnl: float = 0.0
    var_95: float = 0.0
    total_exposure: float = 0.0
    position_count: int = 0
    largest_position_ratio: float = 0.0
    risk_level: RiskLevel = RiskLevel.LOW
    violations: List[str] = None
    
    def __post_init__(self):
        if self.violations is None:
            self.violations = []


class IRiskRule(ABC):
    """风险规则接口"""
    
    @abstractmethod
    async def check_signal(self, signal: Signal, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查信号是否符合风险规则"""
        pass
    
    @abstractmethod
    async def check_order(self, order: Order, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查订单是否符合风险规则"""
        pass
    
    @abstractmethod
    def get_rule_name(self) -> str:
        """获取规则名称"""
        pass


class PositionSizeRule(IRiskRule):
    """仓位大小规则"""
    
    def __init__(self, max_position_size: float = 0.25):
        self.max_position_size = max_position_size
        self.logger = Logger.get_logger("risk.position_size")
    
    async def check_signal(self, signal: Signal, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查信号的仓位大小"""
        if not signal.price:
            return True, ""
        
        # 计算建议仓位大小
        from domain.services import PortfolioService
        suggested_quantity = PortfolioService.calculate_position_size(
            portfolio, signal, max_position_size=self.max_position_size
        )
        
        # 计算仓位比例
        position_value = signal.price.value * suggested_quantity.value
        portfolio_value = portfolio.total_value.value
        
        if portfolio_value > 0:
            position_ratio = float(position_value / portfolio_value)
            if position_ratio > self.max_position_size:
                return False, f"仓位比例超限: {position_ratio:.2%} > {self.max_position_size:.2%}"
        
        return True, ""
    
    async def check_order(self, order: Order, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查订单的仓位大小"""
        if not order.price:
            return True, ""
        
        # 计算订单价值
        order_value = order.price.value * order.quantity.value
        portfolio_value = portfolio.total_value.value
        
        if portfolio_value > 0:
            position_ratio = float(order_value / portfolio_value)
            if position_ratio > self.max_position_size:
                return False, f"订单仓位比例超限: {position_ratio:.2%} > {self.max_position_size:.2%}"
        
        return True, ""
    
    def get_rule_name(self) -> str:
        return "PositionSizeRule"


class DrawdownRule(IRiskRule):
    """回撤规则"""
    
    def __init__(self, max_drawdown: float = 0.2):
        self.max_drawdown = max_drawdown
        self.logger = Logger.get_logger("risk.drawdown")
    
    async def check_signal(self, signal: Signal, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查当前回撤水平"""
        current_drawdown = self._calculate_current_drawdown(portfolio)
        
        if current_drawdown > self.max_drawdown:
            return False, f"回撤超限，暂停交易: {current_drawdown:.2%} > {self.max_drawdown:.2%}"
        
        return True, ""
    
    async def check_order(self, order: Order, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查订单时的回撤水平"""
        return await self.check_signal(None, portfolio)
    
    def _calculate_current_drawdown(self, portfolio: Portfolio) -> float:
        """计算当前回撤"""
        initial_value = float(portfolio.account.initial_balance.value)
        current_value = float(portfolio.total_value.value)
        
        if initial_value == 0:
            return 0.0
        
        return max(0.0, (initial_value - current_value) / initial_value)
    
    def get_rule_name(self) -> str:
        return "DrawdownRule"


class ExposureRule(IRiskRule):
    """敞口规则"""
    
    def __init__(self, max_total_exposure: float = 0.8):
        self.max_total_exposure = max_total_exposure
        self.logger = Logger.get_logger("risk.exposure")
    
    async def check_signal(self, signal: Signal, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查总敞口"""
        current_exposure = self._calculate_total_exposure(portfolio)
        
        if current_exposure > self.max_total_exposure:
            return False, f"总敞口超限: {current_exposure:.2%} > {self.max_total_exposure:.2%}"
        
        return True, ""
    
    async def check_order(self, order: Order, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查订单后的总敞口"""
        current_exposure = self._calculate_total_exposure(portfolio)
        
        # 估算新订单的敞口影响
        if order.price and order.side == OrderSide.BUY:
            order_value = order.price.value * order.quantity.value
            portfolio_value = portfolio.total_value.value
            
            if portfolio_value > 0:
                additional_exposure = float(order_value / portfolio_value)
                projected_exposure = current_exposure + additional_exposure
                
                if projected_exposure > self.max_total_exposure:
                    return False, f"订单将导致敞口超限: {projected_exposure:.2%} > {self.max_total_exposure:.2%}"
        
        return True, ""
    
    def _calculate_total_exposure(self, portfolio: Portfolio) -> float:
        """计算总敞口"""
        total_market_value = portfolio.total_market_value.value
        total_value = portfolio.total_value.value
        
        if total_value == 0:
            return 0.0
        
        return float(total_market_value / total_value)
    
    def get_rule_name(self) -> str:
        return "ExposureRule"


class CorrelationRule(IRiskRule):
    """相关性规则"""
    
    def __init__(self, max_correlation: float = 0.7):
        self.max_correlation = max_correlation
        self.logger = Logger.get_logger("risk.correlation")
        self._correlation_cache: Dict[str, Dict[str, float]] = {}
    
    async def check_signal(self, signal: Signal, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查与现有持仓的相关性"""
        # 简化实现：检查是否已有相同品种的持仓
        existing_position = portfolio.get_position(signal.symbol)
        if existing_position and existing_position.is_open:
            return False, f"已存在{signal.symbol}的持仓，避免重复敞口"
        
        # 实际实现中应该计算品种间的价格相关性
        return True, ""
    
    async def check_order(self, order: Order, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查订单的相关性"""
        # 简化实现
        existing_position = portfolio.get_position(order.symbol)
        if existing_position and existing_position.is_open and order.side == OrderSide.BUY:
            return False, f"已存在{order.symbol}的持仓，避免加仓"
        
        return True, ""
    
    def get_rule_name(self) -> str:
        return "CorrelationRule"


class DailyLossRule(IRiskRule):
    """日损失规则"""
    
    def __init__(self, max_daily_loss: float = 0.05):
        self.max_daily_loss = max_daily_loss
        self.logger = Logger.get_logger("risk.daily_loss")
        self._daily_start_value: Optional[float] = None
        self._last_reset_date: Optional[datetime] = None
    
    async def check_signal(self, signal: Signal, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查日损失"""
        daily_loss_ratio = self._calculate_daily_loss(portfolio)
        
        if daily_loss_ratio > self.max_daily_loss:
            return False, f"日损失超限，暂停交易: {daily_loss_ratio:.2%} > {self.max_daily_loss:.2%}"
        
        return True, ""
    
    async def check_order(self, order: Order, portfolio: Portfolio) -> Tuple[bool, str]:
        """检查订单时的日损失"""
        return await self.check_signal(None, portfolio)
    
    def _calculate_daily_loss(self, portfolio: Portfolio) -> float:
        """计算日损失比例"""
        today = datetime.now().date()
        
        # 重置日开始价值
        if self._last_reset_date != today:
            self._daily_start_value = float(portfolio.total_value.value)
            self._last_reset_date = today
        
        if self._daily_start_value is None or self._daily_start_value == 0:
            return 0.0
        
        current_value = float(portfolio.total_value.value)
        daily_loss = max(0.0, (self._daily_start_value - current_value) / self._daily_start_value)
        
        return daily_loss
    
    def get_rule_name(self) -> str:
        return "DailyLossRule"


class RiskManager:
    """
    风险管理器
    
    风险管理核心模块 - 封装复杂的风险控制逻辑，提供统一的风险管理接口。
    """
    
    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("risk_manager")
        
        # 加载风险限制配置
        self.risk_limits = self._load_risk_limits()
        
        # 初始化风险规则
        self.rules: List[IRiskRule] = []
        self._initialize_rules()
        
        # 风险指标缓存
        self._metrics_cache: Dict[str, RiskMetrics] = {}
        self._cache_lock = threading.RLock()
        
        # 风险事件回调
        self._risk_event_callbacks: List[callable] = []
    
    def _load_risk_limits(self) -> RiskLimits:
        """加载风险限制配置"""
        risk_config = self.config.get_config("strategies.execution.risk_control", {})
        
        return RiskLimits(
            max_position_size=risk_config.get("max_position_size", 0.25),
            max_total_exposure=risk_config.get("max_total_exposure", 0.8),
            max_drawdown=risk_config.get("max_drawdown", 0.2),
            max_daily_loss=risk_config.get("max_daily_loss", 0.05),
            max_var_ratio=risk_config.get("max_var_ratio", 0.1),
            max_correlation=risk_config.get("max_correlation", 0.7),
            max_positions=risk_config.get("max_positions", 10),
            stop_loss_threshold=risk_config.get("stop_loss_threshold", 0.02),
            take_profit_ratio=risk_config.get("take_profit_ratio", 2.0)
        )
    
    def _initialize_rules(self) -> None:
        """初始化风险规则"""
        self.rules = [
            PositionSizeRule(self.risk_limits.max_position_size),
            DrawdownRule(self.risk_limits.max_drawdown),
            ExposureRule(self.risk_limits.max_total_exposure),
            CorrelationRule(self.risk_limits.max_correlation),
            DailyLossRule(self.risk_limits.max_daily_loss)
        ]
        
        self.logger.info(f"风险规则已初始化: {len(self.rules)}个规则")
    
    async def check_signal_risk(self, signal: Signal, portfolio: Portfolio) -> Tuple[bool, List[str]]:
        """
        检查信号风险
        
        Args:
            signal: 交易信号
            portfolio: 投资组合
            
        Returns:
            (是否通过检查, 违规信息列表)
        """
        violations = []
        
        try:
            # 执行所有风险规则检查
            for rule in self.rules:
                try:
                    passed, message = await rule.check_signal(signal, portfolio)
                    if not passed:
                        violations.append(f"{rule.get_rule_name()}: {message}")
                        self.logger.warning(f"信号风险检查失败 - {rule.get_rule_name()}: {message}")
                
                except Exception as e:
                    error_msg = f"{rule.get_rule_name()}执行失败: {e}"
                    violations.append(error_msg)
                    self.logger.error(error_msg)
            
            # 记录风险事件
            if violations:
                await self._trigger_risk_event("signal_rejected", {
                    "signal": signal,
                    "violations": violations
                })
            
            return len(violations) == 0, violations
            
        except Exception as e:
            self.logger.error(f"信号风险检查异常: {e}")
            return False, [f"风险检查异常: {e}"]
    
    async def check_order_risk(self, order: Order, portfolio: Portfolio) -> Tuple[bool, List[str]]:
        """
        检查订单风险
        
        Args:
            order: 订单
            portfolio: 投资组合
            
        Returns:
            (是否通过检查, 违规信息列表)
        """
        violations = []
        
        try:
            # 执行所有风险规则检查
            for rule in self.rules:
                try:
                    passed, message = await rule.check_order(order, portfolio)
                    if not passed:
                        violations.append(f"{rule.get_rule_name()}: {message}")
                        self.logger.warning(f"订单风险检查失败 - {rule.get_rule_name()}: {message}")
                
                except Exception as e:
                    error_msg = f"{rule.get_rule_name()}执行失败: {e}"
                    violations.append(error_msg)
                    self.logger.error(error_msg)
            
            # 记录风险事件
            if violations:
                await self._trigger_risk_event("order_rejected", {
                    "order": order,
                    "violations": violations
                })
            
            return len(violations) == 0, violations
            
        except Exception as e:
            self.logger.error(f"订单风险检查异常: {e}")
            return False, [f"风险检查异常: {e}"]
    
    async def calculate_risk_metrics(self, portfolio: Portfolio) -> RiskMetrics:
        """计算风险指标"""
        try:
            # 计算基本风险指标
            current_drawdown = self._calculate_drawdown(portfolio)
            daily_pnl = self._calculate_daily_pnl(portfolio)
            var_95 = await self._calculate_var(portfolio)
            total_exposure = self._calculate_exposure(portfolio)
            position_count = len(portfolio.get_open_positions())
            largest_position_ratio = self._calculate_largest_position_ratio(portfolio)
            
            # 确定风险等级
            risk_level = self._determine_risk_level(
                current_drawdown, total_exposure, var_95
            )
            
            # 检查违规
            violations = await self._check_all_violations(portfolio)
            
            metrics = RiskMetrics(
                current_drawdown=current_drawdown,
                daily_pnl=daily_pnl,
                var_95=var_95,
                total_exposure=total_exposure,
                position_count=position_count,
                largest_position_ratio=largest_position_ratio,
                risk_level=risk_level,
                violations=violations
            )
            
            # 缓存指标
            with self._cache_lock:
                self._metrics_cache[portfolio.id] = metrics
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算风险指标失败: {e}")
            return RiskMetrics()
    
    def _calculate_drawdown(self, portfolio: Portfolio) -> float:
        """计算回撤"""
        initial_value = float(portfolio.account.initial_balance.value)
        current_value = float(portfolio.total_value.value)
        
        if initial_value == 0:
            return 0.0
        
        return max(0.0, (initial_value - current_value) / initial_value)
    
    def _calculate_daily_pnl(self, portfolio: Portfolio) -> float:
        """计算日盈亏"""
        # 简化实现，实际应该基于交易记录计算
        return 0.0
    
    async def _calculate_var(self, portfolio: Portfolio) -> float:
        """计算VaR"""
        try:
            var_price = RiskCalculationService.calculate_portfolio_var(portfolio)
            total_value = portfolio.total_value.value
            
            if total_value == 0:
                return 0.0
            
            return float(var_price.value / total_value)
            
        except Exception as e:
            self.logger.warning(f"VaR计算失败: {e}")
            return 0.0
    
    def _calculate_exposure(self, portfolio: Portfolio) -> float:
        """计算敞口"""
        total_market_value = portfolio.total_market_value.value
        total_value = portfolio.total_value.value
        
        if total_value == 0:
            return 0.0
        
        return float(total_market_value / total_value)
    
    def _calculate_largest_position_ratio(self, portfolio: Portfolio) -> float:
        """计算最大仓位比例"""
        open_positions = portfolio.get_open_positions()
        if not open_positions:
            return 0.0
        
        total_value = portfolio.total_value.value
        if total_value == 0:
            return 0.0
        
        largest_position_value = max(
            pos.market_value.value for pos in open_positions
        )
        
        return float(largest_position_value / total_value)
    
    def _determine_risk_level(
        self, 
        drawdown: float, 
        exposure: float, 
        var: float
    ) -> RiskLevel:
        """确定风险等级"""
        if (drawdown > 0.15 or exposure > 0.9 or var > 0.08):
            return RiskLevel.EXTREME
        elif (drawdown > 0.1 or exposure > 0.7 or var > 0.06):
            return RiskLevel.HIGH
        elif (drawdown > 0.05 or exposure > 0.5 or var > 0.04):
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    async def _check_all_violations(self, portfolio: Portfolio) -> List[str]:
        """检查所有违规情况"""
        violations = []
        
        # 检查各项限制
        metrics = await self.calculate_risk_metrics(portfolio)
        
        if metrics.current_drawdown > self.risk_limits.max_drawdown:
            violations.append(f"回撤超限: {metrics.current_drawdown:.2%}")
        
        if metrics.total_exposure > self.risk_limits.max_total_exposure:
            violations.append(f"敞口超限: {metrics.total_exposure:.2%}")
        
        if metrics.position_count > self.risk_limits.max_positions:
            violations.append(f"持仓数量超限: {metrics.position_count}")
        
        if metrics.largest_position_ratio > self.risk_limits.max_position_size:
            violations.append(f"单个仓位超限: {metrics.largest_position_ratio:.2%}")
        
        return violations
    
    async def _trigger_risk_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """触发风险事件"""
        for callback in self._risk_event_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event_type, data)
                else:
                    callback(event_type, data)
            except Exception as e:
                self.logger.error(f"风险事件回调执行失败: {e}")
    
    def add_risk_event_callback(self, callback: callable) -> None:
        """添加风险事件回调"""
        self._risk_event_callbacks.append(callback)
    
    def remove_risk_event_callback(self, callback: callable) -> None:
        """移除风险事件回调"""
        if callback in self._risk_event_callbacks:
            self._risk_event_callbacks.remove(callback)
    
    def get_cached_metrics(self, portfolio_id: str) -> Optional[RiskMetrics]:
        """获取缓存的风险指标"""
        with self._cache_lock:
            return self._metrics_cache.get(portfolio_id)
