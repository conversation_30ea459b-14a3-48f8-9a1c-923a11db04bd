"""
个人量化交易系统 (Personal Quantitative Trading System)

基于Python 3.13构建的现代化量化交易系统，采用分层架构设计，
支持多策略并行执行、实时数据处理、回测分析等功能。

Architecture:
- Presentation Layer: GUI components, controllers, view models
- Application Layer: Application services, orchestrators, commands
- Domain Layer: Entities, value objects, repositories, domain services
- Infrastructure Layer: Data sources, repositories, external services, config
- Shared Layer: Exceptions, utilities, constants

Features:
- Free-threaded execution support (Python 3.13)
- Real-time data processing
- Strategy engine with hot-loading
- Advanced backtesting capabilities
- Risk management system
- Plugin architecture
- Performance optimization

Author: Augment Agent
Version: 1.0.0
Python: 3.13+
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"
__python_requires__ = ">=3.13"

# 导入核心模块
from .shared import *
from .domain import *
from .infrastructure import *
from .application import *
from .presentation import *

__all__ = [
    "__version__",
    "__author__",
    "__python_requires__",
]
