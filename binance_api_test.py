#!/usr/bin/env python3
"""
Binance API 测试脚本

用于验证 Binance API 配置是否正确，测试连接和数据获取功能。
基于最新的 Binance API 文档 (2025-07-10) 进行优化。
"""

import os
import asyncio
import json
import websockets
import aiohttp
from datetime import datetime
from typing import Dict, Any, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BinanceAPITester:
    """Binance API 测试器"""
    
    def __init__(self, use_testnet: bool = True):
        self.use_testnet = use_testnet
        
        # 设置 API 端点
        if use_testnet:
            self.base_url = "https://testnet.binance.vision/api/v3"
            self.ws_url = "wss://testnet.binance.vision/ws"
        else:
            self.base_url = "https://api.binance.com/api/v3"
            self.ws_url = "wss://stream.binance.com:9443/ws"
        
        # 获取 API 密钥
        self.api_key = os.getenv('BINANCE_API_KEY', '')
        self.secret_key = os.getenv('BINANCE_SECRET_KEY', '')
        
        logger.info(f"使用 {'测试网' if use_testnet else '生产网'}")
        logger.info(f"API Key: {'已设置' if self.api_key else '未设置'}")
        logger.info(f"Secret Key: {'已设置' if self.secret_key else '未设置'}")
    
    async def test_server_time(self) -> bool:
        """测试服务器时间 - 验证基本连接"""
        try:
            url = f"{self.base_url}/time"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        server_time = datetime.fromtimestamp(data['serverTime'] / 1000)
                        logger.info(f"✅ 服务器连接成功")
                        logger.info(f"   服务器时间: {server_time}")
                        return True
                    else:
                        logger.error(f"❌ 服务器连接失败: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ 服务器连接异常: {e}")
            return False
    
    async def test_exchange_info(self) -> bool:
        """测试交易所信息 - 验证 API 可用性"""
        try:
            url = f"{self.base_url}/exchangeInfo"
            params = {'symbol': 'BTCUSDT'}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        symbols = data.get('symbols', [])
                        if symbols:
                            symbol_info = symbols[0]
                            logger.info(f"✅ 交易所信息获取成功")
                            logger.info(f"   交易对: {symbol_info['symbol']}")
                            logger.info(f"   状态: {symbol_info['status']}")
                            return True
                        else:
                            logger.error("❌ 未找到交易对信息")
                            return False
                    else:
                        logger.error(f"❌ 交易所信息获取失败: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ 交易所信息获取异常: {e}")
            return False
    
    async def test_ticker_data(self) -> bool:
        """测试24小时价格数据"""
        try:
            url = f"{self.base_url}/ticker/24hr"
            params = {'symbol': 'BTCUSDT'}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"✅ 24小时价格数据获取成功")
                        logger.info(f"   交易对: {data['symbol']}")
                        logger.info(f"   当前价格: {data['lastPrice']}")
                        logger.info(f"   24h变化: {data['priceChangePercent']}%")
                        logger.info(f"   24h成交量: {data['volume']}")
                        return True
                    else:
                        logger.error(f"❌ 价格数据获取失败: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ 价格数据获取异常: {e}")
            return False
    
    async def test_websocket_connection(self, duration: int = 10) -> bool:
        """测试 WebSocket 连接 - 验证实时数据流"""
        try:
            # 使用 ticker 流测试
            stream_url = f"{self.ws_url}/btcusdt@ticker"
            logger.info(f"🔗 测试 WebSocket 连接: {stream_url}")
            
            # 根据最新文档，ping 间隔为 20 秒
            connect_kwargs = {
                'ping_interval': 20,
                'ping_timeout': 60,  # 1分钟超时
                'close_timeout': 10
            }
            
            message_count = 0
            async with websockets.connect(stream_url, **connect_kwargs) as websocket:
                logger.info(f"✅ WebSocket 连接成功，监听 {duration} 秒...")
                
                # 设置超时
                try:
                    async with asyncio.timeout(duration):
                        async for message in websocket:
                            data = json.loads(message)
                            message_count += 1
                            
                            if message_count == 1:
                                logger.info(f"✅ 收到第一条消息:")
                                logger.info(f"   交易对: {data.get('s', 'N/A')}")
                                logger.info(f"   价格: {data.get('c', 'N/A')}")
                                logger.info(f"   时间: {datetime.fromtimestamp(data.get('E', 0) / 1000)}")
                            
                            if message_count >= 3:  # 收到3条消息后停止
                                break
                                
                except asyncio.TimeoutError:
                    pass
                
                logger.info(f"✅ WebSocket 测试完成，共收到 {message_count} 条消息")
                return message_count > 0
                
        except Exception as e:
            logger.error(f"❌ WebSocket 连接异常: {e}")
            return False
    
    async def test_kline_data(self) -> bool:
        """测试K线数据"""
        try:
            url = f"{self.base_url}/klines"
            params = {
                'symbol': 'BTCUSDT',
                'interval': '1h',
                'limit': 5
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"✅ K线数据获取成功")
                        logger.info(f"   获取到 {len(data)} 条K线数据")
                        if data:
                            latest = data[-1]
                            open_time = datetime.fromtimestamp(latest[0] / 1000)
                            logger.info(f"   最新K线时间: {open_time}")
                            logger.info(f"   开盘价: {latest[1]}")
                            logger.info(f"   收盘价: {latest[4]}")
                        return True
                    else:
                        logger.error(f"❌ K线数据获取失败: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ K线数据获取异常: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        logger.info("🚀 开始 Binance API 测试...")
        logger.info("=" * 50)
        
        results = {}
        
        # 基础连接测试
        logger.info("📡 测试基础连接...")
        results['server_time'] = await self.test_server_time()
        
        # 交易所信息测试
        logger.info("\n📊 测试交易所信息...")
        results['exchange_info'] = await self.test_exchange_info()
        
        # 价格数据测试
        logger.info("\n💰 测试价格数据...")
        results['ticker_data'] = await self.test_ticker_data()
        
        # K线数据测试
        logger.info("\n📈 测试K线数据...")
        results['kline_data'] = await self.test_kline_data()
        
        # WebSocket 测试
        logger.info("\n🔗 测试 WebSocket 连接...")
        results['websocket'] = await self.test_websocket_connection()
        
        # 输出测试结果
        logger.info("\n" + "=" * 50)
        logger.info("📋 测试结果汇总:")
        
        all_passed = True
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"   {test_name}: {status}")
            if not result:
                all_passed = False
        
        logger.info("\n" + "=" * 50)
        if all_passed:
            logger.info("🎉 所有测试通过！Binance API 配置正确。")
        else:
            logger.info("⚠️  部分测试失败，请检查配置和网络连接。")
        
        return results


async def main():
    """主函数"""
    print("Binance API 测试工具")
    print("=" * 50)
    
    # 检查环境变量
    api_key = os.getenv('BINANCE_API_KEY')
    secret_key = os.getenv('BINANCE_SECRET_KEY')
    
    if not api_key or not secret_key:
        print("⚠️  警告: 未检测到 API 密钥环境变量")
        print("   请设置 BINANCE_API_KEY 和 BINANCE_SECRET_KEY")
        print("   对于只读操作（如本测试），API 密钥不是必需的")
        print()
    
    # 选择测试环境
    use_testnet = True  # 默认使用测试网
    
    # 创建测试器并运行测试
    tester = BinanceAPITester(use_testnet=use_testnet)
    results = await tester.run_all_tests()
    
    return results


if __name__ == "__main__":
    asyncio.run(main())
