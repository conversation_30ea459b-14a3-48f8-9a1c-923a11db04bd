"""
配置管理模块

提供统一的配置管理功能，支持多种配置源和动态配置更新。
利用Python 3.13的新特性提供更好的性能和功能。
"""

import os
import yaml
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, field
import threading
from datetime import datetime

from shared.exceptions import (
    ConfigurationException, ConfigurationMissingException, 
    ConfigurationValidationException
)
from shared.utils import Logger, FileUtils
from shared.constants import DEFAULT_CONFIG


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    key: str
    old_value: Any
    new_value: Any
    timestamp: datetime = field(default_factory=datetime.utcnow)


class ConfigurationManager:
    """
    配置管理器
    
    深度模块封装配置复杂性，提供统一的配置接口。
    支持多种配置源、配置验证、动态更新和变更通知。
    """
    
    def __init__(self, config_dir: Union[str, Path] = "config"):
        self.config_dir = Path(config_dir)
        self._config_cache: Dict[str, Any] = {}
        self._validators: Dict[str, Callable[[Any], bool]] = {}
        self._change_listeners: List[Callable[[ConfigChangeEvent], None]] = []
        self._lock = threading.RLock()
        self._logger = Logger.get_logger("config")
        
        # 确保配置目录存在
        FileUtils.ensure_directory(self.config_dir)
        
        # 加载默认配置
        self._load_default_config()
        
        # 加载配置文件
        self._load_config_files()
    
    def _load_default_config(self) -> None:
        """加载默认配置"""
        with self._lock:
            self._config_cache.update(DEFAULT_CONFIG)
            self._logger.info("已加载默认配置")
    
    def _load_config_files(self) -> None:
        """加载配置文件"""
        config_files = [
            "system.yaml",
            "data_sources.yaml", 
            "strategies.yaml"
        ]
        
        for config_file in config_files:
            file_path = self.config_dir / config_file
            if file_path.exists():
                try:
                    self._load_yaml_file(file_path)
                    self._logger.info(f"已加载配置文件: {config_file}")
                except Exception as e:
                    self._logger.warning(f"加载配置文件失败 {config_file}: {e}")
            else:
                self._logger.warning(f"配置文件不存在: {config_file}")
    
    def _load_yaml_file(self, file_path: Path) -> None:
        """加载YAML配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 处理环境变量替换
            content = self._substitute_env_vars(content)
            
            # 解析YAML
            config_data = yaml.safe_load(content)
            
            if config_data:
                with self._lock:
                    self._merge_config(self._config_cache, config_data)
                    
        except yaml.YAMLError as e:
            raise ConfigurationException(
                f"YAML解析错误: {e}",
                config_file=str(file_path)
            )
        except Exception as e:
            raise ConfigurationException(
                f"配置文件加载错误: {e}",
                config_file=str(file_path)
            )
    
    def _substitute_env_vars(self, content: str) -> str:
        """替换环境变量"""
        import re
        
        def replace_env_var(match):
            var_name = match.group(1)
            default_value = match.group(2) if match.group(2) else ""
            return os.getenv(var_name, default_value)
        
        # 匹配 ${VAR_NAME} 或 ${VAR_NAME:default_value} 格式
        pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
        return re.sub(pattern, replace_env_var, content)
    
    def _merge_config(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """递归合并配置"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_config(target[key], value)
            else:
                target[key] = value
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键（如 "system.logging.level"）
            default: 默认值
            
        Returns:
            配置值
        """
        with self._lock:
            try:
                keys = key.split('.')
                value = self._config_cache
                
                for k in keys:
                    if isinstance(value, dict) and k in value:
                        value = value[k]
                    else:
                        return default
                
                return value
                
            except Exception as e:
                self._logger.error(f"获取配置失败 {key}: {e}")
                return default
    
    def set_config(self, key: str, value: Any, validate: bool = True) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            validate: 是否验证配置值
        """
        if validate and not self._validate_config(key, value):
            raise ConfigurationValidationException(
                f"配置值验证失败: {key} = {value}",
                config_key=key
            )
        
        with self._lock:
            old_value = self.get_config(key)
            
            # 设置配置值
            keys = key.split('.')
            config = self._config_cache
            
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            config[keys[-1]] = value
            
            # 触发变更事件
            event = ConfigChangeEvent(key, old_value, value)
            self._notify_change_listeners(event)
            
            self._logger.info(f"配置已更新: {key} = {value}")
    
    def has_config(self, key: str) -> bool:
        """检查配置是否存在"""
        return self.get_config(key) is not None
    
    def get_required_config(self, key: str) -> Any:
        """
        获取必需的配置值
        
        Args:
            key: 配置键
            
        Returns:
            配置值
            
        Raises:
            ConfigurationMissingException: 配置不存在
        """
        value = self.get_config(key)
        if value is None:
            raise ConfigurationMissingException(
                f"必需的配置不存在: {key}",
                config_key=key
            )
        return value
    
    def register_validator(self, key: str, validator: Callable[[Any], bool]) -> None:
        """
        注册配置验证器
        
        Args:
            key: 配置键
            validator: 验证函数
        """
        with self._lock:
            self._validators[key] = validator
            self._logger.debug(f"已注册配置验证器: {key}")
    
    def _validate_config(self, key: str, value: Any) -> bool:
        """验证配置值"""
        validator = self._validators.get(key)
        if validator:
            try:
                return validator(value)
            except Exception as e:
                self._logger.error(f"配置验证器执行失败 {key}: {e}")
                return False
        return True
    
    def add_change_listener(self, listener: Callable[[ConfigChangeEvent], None]) -> None:
        """
        添加配置变更监听器
        
        Args:
            listener: 监听器函数
        """
        with self._lock:
            self._change_listeners.append(listener)
            self._logger.debug("已添加配置变更监听器")
    
    def remove_change_listener(self, listener: Callable[[ConfigChangeEvent], None]) -> None:
        """移除配置变更监听器"""
        with self._lock:
            if listener in self._change_listeners:
                self._change_listeners.remove(listener)
                self._logger.debug("已移除配置变更监听器")
    
    def _notify_change_listeners(self, event: ConfigChangeEvent) -> None:
        """通知配置变更监听器"""
        for listener in self._change_listeners:
            try:
                listener(event)
            except Exception as e:
                self._logger.error(f"配置变更监听器执行失败: {e}")
    
    def reload_config(self) -> None:
        """重新加载配置"""
        with self._lock:
            self._config_cache.clear()
            self._load_default_config()
            self._load_config_files()
            self._logger.info("配置已重新加载")
    
    def save_config_to_file(self, file_name: str, config_section: Optional[str] = None) -> None:
        """
        保存配置到文件
        
        Args:
            file_name: 文件名
            config_section: 配置节（如果为None则保存全部配置）
        """
        file_path = self.config_dir / file_name
        
        with self._lock:
            if config_section:
                config_data = self.get_config(config_section, {})
            else:
                config_data = self._config_cache.copy()
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            self._logger.info(f"配置已保存到文件: {file_path}")
            
        except Exception as e:
            raise ConfigurationException(
                f"保存配置文件失败: {e}",
                config_file=str(file_path)
            )
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        with self._lock:
            return self._config_cache.copy()
    
    def validate_all_config(self) -> List[str]:
        """
        验证所有配置
        
        Returns:
            验证错误列表
        """
        errors = []
        
        with self._lock:
            for key, validator in self._validators.items():
                value = self.get_config(key)
                if value is not None:
                    try:
                        if not validator(value):
                            errors.append(f"配置验证失败: {key} = {value}")
                    except Exception as e:
                        errors.append(f"配置验证器执行失败 {key}: {e}")
        
        return errors


class EnvironmentManager:
    """
    环境变量管理器
    
    安全性和灵活性平衡的环境变量管理。
    """
    
    @staticmethod
    def get_secure_config(key: str, default: Optional[str] = None) -> str:
        """
        获取敏感配置
        
        Args:
            key: 环境变量键
            default: 默认值
            
        Returns:
            环境变量值
            
        Raises:
            ConfigurationMissingException: 必需的环境变量不存在
        """
        value = os.getenv(key, default)
        if value is None:
            raise ConfigurationMissingException(f"必需的环境变量不存在: {key}")
        return value
    
    @staticmethod
    def get_bool_config(key: str, default: bool = False) -> bool:
        """获取布尔类型的环境变量"""
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    @staticmethod
    def get_int_config(key: str, default: int = 0) -> int:
        """获取整数类型的环境变量"""
        try:
            return int(os.getenv(key, str(default)))
        except ValueError:
            return default
    
    @staticmethod
    def get_float_config(key: str, default: float = 0.0) -> float:
        """获取浮点数类型的环境变量"""
        try:
            return float(os.getenv(key, str(default)))
        except ValueError:
            return default
    
    @staticmethod
    def get_list_config(key: str, separator: str = ',', default: Optional[List[str]] = None) -> List[str]:
        """获取列表类型的环境变量"""
        value = os.getenv(key)
        if value is None:
            return default or []
        return [item.strip() for item in value.split(separator) if item.strip()]
    
    @staticmethod
    def set_env_var(key: str, value: str) -> None:
        """设置环境变量"""
        os.environ[key] = value
    
    @staticmethod
    def load_env_file(file_path: Union[str, Path]) -> None:
        """从.env文件加载环境变量"""
        file_path = Path(file_path)
        if not file_path.exists():
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"\'')
                        os.environ[key] = value
        except Exception as e:
            raise ConfigurationException(f"加载环境变量文件失败: {e}")


# 全局配置管理器实例
_global_config_manager: Optional[ConfigurationManager] = None


def get_config_manager() -> ConfigurationManager:
    """获取全局配置管理器实例"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = ConfigurationManager()
    return _global_config_manager


def initialize_config(config_dir: Union[str, Path] = "config") -> ConfigurationManager:
    """初始化全局配置管理器"""
    global _global_config_manager
    _global_config_manager = ConfigurationManager(config_dir)
    return _global_config_manager
