"""
回测引擎模块

回测引擎核心模块 - 封装复杂的回测逻辑，提供完整的回测功能。
"""

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, field
from uuid import uuid4
import threading

from domain.value_objects import MarketData, Signal, Order, Price, Volume, PerformanceMetrics
from domain.entities import Portfolio, Position, Transaction, Account
from domain.services import PerformanceCalculationService
from shared.constants import (
    SignalType, OrderType, OrderSide, OrderStatus, PositionSide,
    BacktestStatus, TimeFrame
)
from shared.exceptions import BacktestException, BacktestExecutionException
from shared.utils import Logger, PerformanceUtils, DateTimeUtils
from infrastructure.config import ConfigurationManager
from infrastructure.data_manager import DataManager, DataRequest
from application.strategies import IStrategy, StrategyContext
from application.strategy_engine import StrategyEngine, SignalBus


@dataclass
class BacktestConfig:
    """回测配置"""
    strategy_id: str
    symbol: str
    start_date: datetime
    end_date: datetime
    initial_capital: float = 100000.0
    commission: float = 0.001  # 0.1%
    slippage: float = 0.0001   # 0.01%
    timeframe: TimeFrame = TimeFrame.D1
    benchmark_symbol: Optional[str] = None
    risk_free_rate: float = 0.02
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BacktestResult:
    """回测结果"""
    backtest_id: str
    config: BacktestConfig
    performance: PerformanceMetrics
    portfolio_history: List[Dict[str, Any]] = field(default_factory=list)
    trade_history: List[Transaction] = field(default_factory=list)
    signal_history: List[Signal] = field(default_factory=list)
    daily_returns: List[float] = field(default_factory=list)
    benchmark_returns: List[float] = field(default_factory=list)
    status: BacktestStatus = BacktestStatus.COMPLETED
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class BacktestSimulator:
    """回测模拟器"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.logger = Logger.get_logger("backtest_simulator")
        
        # 初始化投资组合
        self.portfolio = self._create_portfolio()
        
        # 交易记录
        self.trades: List[Transaction] = []
        self.signals: List[Signal] = []
        self.orders: List[Order] = []
        
        # 性能跟踪
        self.portfolio_values: List[Tuple[datetime, float]] = []
        self.daily_returns: List[float] = []
        
        # 当前状态
        self.current_date: Optional[datetime] = None
        self.current_data: Optional[MarketData] = None
    
    def _create_portfolio(self) -> Portfolio:
        """创建初始投资组合"""
        account = Account(
            name=f"Backtest_{self.config.strategy_id}",
            initial_balance=Price(Decimal(str(self.config.initial_capital))),
            current_balance=Price(Decimal(str(self.config.initial_capital))),
            available_balance=Price(Decimal(str(self.config.initial_capital)))
        )
        
        portfolio = Portfolio(
            name=f"Backtest_Portfolio_{self.config.strategy_id}",
            account=account
        )
        
        return portfolio
    
    async def process_market_data(self, data: MarketData) -> None:
        """处理市场数据"""
        self.current_date = data.timestamp
        self.current_data = data
        
        # 更新持仓价格
        await self._update_positions(data)
        
        # 记录投资组合价值
        portfolio_value = float(self.portfolio.total_value.value)
        self.portfolio_values.append((data.timestamp, portfolio_value))
        
        # 计算日收益率
        if len(self.portfolio_values) > 1:
            prev_value = self.portfolio_values[-2][1]
            if prev_value > 0:
                daily_return = (portfolio_value - prev_value) / prev_value
                self.daily_returns.append(daily_return)
    
    async def process_signal(self, signal: Signal) -> List[Order]:
        """处理交易信号"""
        self.signals.append(signal)
        
        # 生成订单
        orders = await self._generate_orders_from_signal(signal)
        
        # 执行订单
        for order in orders:
            await self._execute_order(order)
        
        return orders
    
    async def _update_positions(self, data: MarketData) -> None:
        """更新持仓价格"""
        position = self.portfolio.get_position(data.symbol)
        if position and position.is_open:
            position.update_price(data.close)
    
    async def _generate_orders_from_signal(self, signal: Signal) -> List[Order]:
        """从信号生成订单"""
        orders = []
        
        if signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
            # 买入信号
            order = await self._create_buy_order(signal)
            if order:
                orders.append(order)
        
        elif signal.signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
            # 卖出信号
            order = await self._create_sell_order(signal)
            if order:
                orders.append(order)
        
        elif signal.signal_type == SignalType.EXIT:
            # 平仓信号
            order = await self._create_exit_order(signal)
            if order:
                orders.append(order)
        
        return orders
    
    async def _create_buy_order(self, signal: Signal) -> Optional[Order]:
        """创建买入订单"""
        if not signal.price:
            return None
        
        # 检查是否已有持仓
        existing_position = self.portfolio.get_position(signal.symbol)
        if existing_position and existing_position.is_open:
            return None
        
        # 计算可买入数量
        available_cash = self.portfolio.account.available_balance.value
        price_with_slippage = signal.price.value * (1 + Decimal(str(self.config.slippage)))
        commission_cost = price_with_slippage * Decimal(str(self.config.commission))
        total_cost_per_share = price_with_slippage + commission_cost
        
        if total_cost_per_share <= 0:
            return None
        
        # 使用固定比例的资金
        position_size_ratio = Decimal('0.1')  # 10%
        max_investment = available_cash * position_size_ratio
        quantity = max_investment / total_cost_per_share
        
        if quantity <= 0:
            return None
        
        order = Order(
            symbol=signal.symbol,
            order_type=OrderType.MARKET,
            side=OrderSide.BUY,
            quantity=Volume(quantity),
            price=Price(price_with_slippage),
            strategy_id=signal.strategy_id,
            signal_id=signal.id
        )
        
        return order
    
    async def _create_sell_order(self, signal: Signal) -> Optional[Order]:
        """创建卖出订单"""
        position = self.portfolio.get_position(signal.symbol)
        if not position or not position.is_open:
            return None
        
        if not signal.price:
            return None
        
        price_with_slippage = signal.price.value * (1 - Decimal(str(self.config.slippage)))
        
        order = Order(
            symbol=signal.symbol,
            order_type=OrderType.MARKET,
            side=OrderSide.SELL,
            quantity=position.quantity,
            price=Price(price_with_slippage),
            strategy_id=signal.strategy_id,
            signal_id=signal.id
        )
        
        return order
    
    async def _create_exit_order(self, signal: Signal) -> Optional[Order]:
        """创建平仓订单"""
        return await self._create_sell_order(signal)
    
    async def _execute_order(self, order: Order) -> None:
        """执行订单"""
        try:
            if order.side == OrderSide.BUY:
                await self._execute_buy_order(order)
            else:
                await self._execute_sell_order(order)
            
            order.status = OrderStatus.FILLED
            order.filled_quantity = order.quantity
            order.average_fill_price = order.price
            
            self.orders.append(order)
            
        except Exception as e:
            self.logger.error(f"订单执行失败: {e}")
            order.status = OrderStatus.REJECTED
            self.orders.append(order)
    
    async def _execute_buy_order(self, order: Order) -> None:
        """执行买入订单"""
        if not order.price:
            raise BacktestExecutionException("买入订单缺少价格信息")
        
        # 计算总成本
        gross_cost = order.price.value * order.quantity.value
        commission = gross_cost * Decimal(str(self.config.commission))
        total_cost = gross_cost + commission
        
        # 检查资金充足性
        if total_cost > self.portfolio.account.available_balance.value:
            raise BacktestExecutionException("资金不足")
        
        # 扣除资金
        self.portfolio.account.withdraw(Price(total_cost))
        
        # 创建持仓
        position = Position(
            symbol=order.symbol,
            side=PositionSide.LONG,
            quantity=order.quantity,
            average_price=order.price,
            current_price=order.price,
            opened_at=self.current_date or datetime.now(),
            strategy_id=order.strategy_id,
            commission_paid=Price(commission)
        )
        
        self.portfolio.add_position(position)
        
        # 记录交易
        transaction = Transaction(
            symbol=order.symbol,
            side=PositionSide.LONG,
            quantity=order.quantity,
            price=order.price,
            commission=Price(commission),
            timestamp=self.current_date or datetime.now(),
            order_id=order.id,
            position_id=position.id,
            strategy_id=order.strategy_id
        )
        
        self.trades.append(transaction)
        self.portfolio.add_transaction(transaction)
    
    async def _execute_sell_order(self, order: Order) -> None:
        """执行卖出订单"""
        if not order.price:
            raise BacktestExecutionException("卖出订单缺少价格信息")
        
        # 获取持仓
        position = self.portfolio.get_position(order.symbol)
        if not position or not position.is_open:
            raise BacktestExecutionException(f"没有{order.symbol}的持仓")
        
        # 检查数量
        if order.quantity.value > position.quantity.value:
            raise BacktestExecutionException("卖出数量超过持仓数量")
        
        # 计算收入
        gross_proceeds = order.price.value * order.quantity.value
        commission = gross_proceeds * Decimal(str(self.config.commission))
        net_proceeds = gross_proceeds - commission
        
        # 增加资金
        self.portfolio.account.deposit(Price(net_proceeds))
        
        # 平仓
        if order.quantity.value == position.quantity.value:
            # 全部平仓
            position.close(order.price, self.current_date)
        else:
            # 部分平仓
            position.partial_close(order.quantity, order.price)
        
        # 记录交易
        transaction = Transaction(
            symbol=order.symbol,
            side=PositionSide.SHORT,  # 卖出记录为SHORT
            quantity=order.quantity,
            price=order.price,
            commission=Price(commission),
            timestamp=self.current_date or datetime.now(),
            order_id=order.id,
            position_id=position.id,
            strategy_id=order.strategy_id
        )
        
        self.trades.append(transaction)
        self.portfolio.add_transaction(transaction)
    
    def get_portfolio_history(self) -> List[Dict[str, Any]]:
        """获取投资组合历史"""
        history = []
        for timestamp, value in self.portfolio_values:
            history.append({
                'timestamp': timestamp,
                'total_value': value,
                'cash': float(self.portfolio.account.current_balance.value),
                'positions_value': float(self.portfolio.total_market_value.value)
            })
        return history


class BacktestEngine:
    """
    回测引擎
    
    回测引擎核心模块 - 封装复杂的回测逻辑，提供完整的回测功能。
    """
    
    def __init__(self, config: ConfigurationManager, data_manager: DataManager):
        self.config = config
        self.data_manager = data_manager
        self.logger = Logger.get_logger("backtest_engine")
        
        # 回测状态
        self._running_backtests: Dict[str, BacktestResult] = {}
        self._lock = threading.RLock()
    
    async def run_backtest(
        self, 
        strategy: IStrategy,
        backtest_config: BacktestConfig,
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> BacktestResult:
        """
        运行回测
        
        Args:
            strategy: 策略实例
            backtest_config: 回测配置
            progress_callback: 进度回调函数
            
        Returns:
            回测结果
        """
        backtest_id = str(uuid4())
        
        try:
            # 创建回测结果
            result = BacktestResult(
                backtest_id=backtest_id,
                config=backtest_config,
                performance=PerformanceMetrics(),
                start_time=datetime.now(),
                status=BacktestStatus.RUNNING
            )
            
            with self._lock:
                self._running_backtests[backtest_id] = result
            
            self.logger.info(f"开始回测: {backtest_id}, 策略: {backtest_config.strategy_id}")
            
            # 获取历史数据
            data_request = DataRequest(
                symbol=backtest_config.symbol,
                timeframe=backtest_config.timeframe,
                start_date=backtest_config.start_date,
                end_date=backtest_config.end_date
            )
            
            market_data_set = await self.data_manager.get_market_data(data_request)
            market_data = market_data_set.data
            
            if not market_data:
                raise BacktestException("没有可用的历史数据")
            
            # 创建回测模拟器
            simulator = BacktestSimulator(backtest_config)
            
            # 初始化策略
            context = StrategyContext(
                strategy_id=backtest_config.strategy_id,
                config=strategy.config,
                portfolio=simulator.portfolio
            )
            await strategy.initialize(context)
            await strategy.start()
            
            # 执行回测
            total_data_points = len(market_data)
            
            for i, data in enumerate(market_data):
                try:
                    # 更新进度
                    if progress_callback:
                        progress = (i + 1) / total_data_points
                        progress_callback(progress)
                    
                    # 处理市场数据
                    await simulator.process_market_data(data)
                    
                    # 更新策略上下文
                    context.current_data = data
                    context.historical_data.append(data)
                    strategy.update_context(context)
                    
                    # 生成信号
                    signals = await strategy.on_data(data)
                    
                    # 处理信号
                    for signal in signals:
                        orders = await simulator.process_signal(signal)
                        result.signal_history.extend([signal])
                
                except Exception as e:
                    self.logger.error(f"回测数据处理失败: {e}")
                    continue
            
            # 停止策略
            await strategy.stop()
            
            # 计算性能指标
            result.performance = await self._calculate_performance(simulator, backtest_config)
            result.portfolio_history = simulator.get_portfolio_history()
            result.trade_history = simulator.trades
            result.daily_returns = simulator.daily_returns
            
            # 获取基准数据（如果配置了）
            if backtest_config.benchmark_symbol:
                result.benchmark_returns = await self._get_benchmark_returns(
                    backtest_config.benchmark_symbol,
                    backtest_config.start_date,
                    backtest_config.end_date,
                    backtest_config.timeframe
                )
            
            result.status = BacktestStatus.COMPLETED
            result.end_time = datetime.now()
            
            self.logger.info(f"回测完成: {backtest_id}, 总收益率: {result.performance.total_return:.2%}")
            
            return result
        
        except Exception as e:
            self.logger.error(f"回测执行失败: {e}")
            
            result.status = BacktestStatus.FAILED
            result.error_message = str(e)
            result.end_time = datetime.now()
            
            return result
        
        finally:
            with self._lock:
                if backtest_id in self._running_backtests:
                    del self._running_backtests[backtest_id]
    
    async def _calculate_performance(
        self, 
        simulator: BacktestSimulator, 
        config: BacktestConfig
    ) -> PerformanceMetrics:
        """计算性能指标"""
        try:
            # 基本收益率计算
            initial_value = config.initial_capital
            final_value = float(simulator.portfolio.total_value.value)
            total_return = (final_value - initial_value) / initial_value
            
            # 计算年化收益率
            days = (config.end_date - config.start_date).days
            annualized_return = (1 + total_return) ** (365.25 / days) - 1 if days > 0 else 0
            
            # 计算其他指标
            returns = simulator.daily_returns
            
            if len(returns) > 1:
                import statistics
                volatility = statistics.stdev(returns) * (252 ** 0.5)  # 年化波动率
                sharpe_ratio = (annualized_return - config.risk_free_rate) / volatility if volatility > 0 else 0
                
                # 计算最大回撤
                portfolio_values = [value for _, value in simulator.portfolio_values]
                max_drawdown = self._calculate_max_drawdown(portfolio_values)
                
                # 计算胜率
                winning_trades = sum(1 for r in returns if r > 0)
                win_rate = winning_trades / len(returns)
                
                # 计算盈亏比
                winning_returns = [r for r in returns if r > 0]
                losing_returns = [r for r in returns if r < 0]
                
                avg_win = statistics.mean(winning_returns) if winning_returns else 0
                avg_loss = abs(statistics.mean(losing_returns)) if losing_returns else 0
                profit_factor = avg_win / avg_loss if avg_loss > 0 else 0
                
            else:
                volatility = 0
                sharpe_ratio = 0
                max_drawdown = 0
                win_rate = 0
                profit_factor = 0
            
            return PerformanceMetrics(
                total_return=total_return,
                annualized_return=annualized_return,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor
            )
        
        except Exception as e:
            self.logger.error(f"性能指标计算失败: {e}")
            return PerformanceMetrics()
    
    def _calculate_max_drawdown(self, values: List[float]) -> float:
        """计算最大回撤"""
        if not values:
            return 0.0
        
        peak = values[0]
        max_dd = 0.0
        
        for value in values:
            if value > peak:
                peak = value
            else:
                drawdown = (peak - value) / peak if peak > 0 else 0
                max_dd = max(max_dd, drawdown)
        
        return max_dd
    
    async def _get_benchmark_returns(
        self, 
        benchmark_symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: TimeFrame
    ) -> List[float]:
        """获取基准收益率"""
        try:
            data_request = DataRequest(
                symbol=benchmark_symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date
            )
            
            benchmark_data_set = await self.data_manager.get_market_data(data_request)
            benchmark_data = benchmark_data_set.data
            
            if not benchmark_data:
                return []
            
            returns = []
            for i in range(1, len(benchmark_data)):
                prev_price = float(benchmark_data[i-1].close.value)
                curr_price = float(benchmark_data[i].close.value)
                
                if prev_price > 0:
                    return_rate = (curr_price - prev_price) / prev_price
                    returns.append(return_rate)
            
            return returns
        
        except Exception as e:
            self.logger.error(f"获取基准数据失败: {e}")
            return []
    
    def get_running_backtests(self) -> Dict[str, BacktestResult]:
        """获取正在运行的回测"""
        with self._lock:
            return self._running_backtests.copy()
    
    def cancel_backtest(self, backtest_id: str) -> bool:
        """取消回测"""
        with self._lock:
            if backtest_id in self._running_backtests:
                result = self._running_backtests[backtest_id]
                result.status = BacktestStatus.CANCELLED
                result.end_time = datetime.now()
                del self._running_backtests[backtest_id]
                self.logger.info(f"回测已取消: {backtest_id}")
                return True
            return False
