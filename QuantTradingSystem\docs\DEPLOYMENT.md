# 部署指南

本文档介绍如何在不同环境中部署量化交易系统。

## 系统要求

### 硬件要求

**最低配置:**
- CPU: 4核心 2.0GHz
- 内存: 8GB RAM
- 存储: 50GB 可用空间
- 网络: 稳定的互联网连接

**推荐配置:**
- CPU: 8核心 3.0GHz+
- 内存: 16GB+ RAM
- 存储: 100GB+ SSD
- 网络: 低延迟网络连接

**高性能配置:**
- CPU: 16核心 3.5GHz+
- 内存: 32GB+ RAM
- 存储: 500GB+ NVMe SSD
- 网络: 专线网络连接

### 软件要求

- **操作系统**: Windows 10+, Ubuntu 20.04+, macOS 11+
- **Python**: 3.13+ (推荐使用最新版本)
- **数据库**: SQLite (内置) 或 PostgreSQL 12+
- **缓存**: Redis 6.0+ (可选)

## 环境准备

### 1. Python环境设置

#### 使用pyenv管理Python版本

```bash
# 安装pyenv (Linux/macOS)
curl https://pyenv.run | bash

# 安装Python 3.13
pyenv install 3.13.0
pyenv global 3.13.0

# 验证版本
python --version
```

#### 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### 2. 依赖安装

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装可选依赖
pip install redis psycopg2-binary  # 数据库支持
pip install scikit-optimize optuna  # 高级优化
pip install matplotlib plotly       # 图表支持
```

### 3. 系统配置

#### 创建配置目录

```bash
mkdir -p config logs data
```

#### 环境变量设置

创建 `.env` 文件：

```bash
# Python优化
PYTHONOPTIMIZE=2
PYTHONUNBUFFERED=1

# 自由线程模式 (Python 3.13)
PYTHON_GIL=0

# 系统配置
QUANT_CONFIG_DIR=config
QUANT_LOG_LEVEL=INFO
QUANT_DATA_DIR=data

# 数据库配置
DATABASE_URL=sqlite:///data/trading.db
REDIS_URL=redis://localhost:6379/0

# API密钥 (根据需要设置)
BINANCE_API_KEY=your_api_key
BINANCE_SECRET_KEY=your_secret_key
ALPHA_VANTAGE_API_KEY=your_api_key
```

## 部署方式

### 1. 开发环境部署

适用于开发和测试。

```bash
# 克隆项目
git clone <repository-url>
cd QuantTradingSystem

# 安装依赖
pip install -r requirements.txt

# 运行系统
python run.py
```

### 2. 生产环境部署

#### 使用Docker部署

创建 `Dockerfile`:

```dockerfile
FROM python:3.13-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p logs data config

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHON_GIL=0

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "main.py", "--mode", "server"]
```

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  trading-system:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./config:/app/config
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHON_GIL=0
      - QUANT_LOG_LEVEL=INFO
    depends_on:
      - redis
      - postgres
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trading_user
      POSTGRES_PASSWORD: trading_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
```

部署命令：

```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f trading-system

# 停止服务
docker-compose down
```

#### 使用systemd部署 (Linux)

创建服务文件 `/etc/systemd/system/trading-system.service`:

```ini
[Unit]
Description=Quantitative Trading System
After=network.target

[Service]
Type=simple
User=trading
Group=trading
WorkingDirectory=/opt/trading-system
Environment=PYTHON_GIL=0
Environment=PYTHONPATH=/opt/trading-system
ExecStart=/opt/trading-system/venv/bin/python main.py --mode server
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

部署步骤：

```bash
# 创建用户
sudo useradd -r -s /bin/false trading

# 复制文件
sudo cp -r QuantTradingSystem /opt/trading-system
sudo chown -R trading:trading /opt/trading-system

# 启用服务
sudo systemctl enable trading-system
sudo systemctl start trading-system

# 查看状态
sudo systemctl status trading-system
```

### 3. 云平台部署

#### AWS部署

使用AWS ECS部署：

```yaml
# ecs-task-definition.json
{
  "family": "trading-system",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "trading-system",
      "image": "your-account.dkr.ecr.region.amazonaws.com/trading-system:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "PYTHON_GIL",
          "value": "0"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/trading-system",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### Azure部署

使用Azure Container Instances：

```yaml
# azure-container-group.yaml
apiVersion: 2019-12-01
location: eastus
name: trading-system
properties:
  containers:
  - name: trading-system
    properties:
      image: your-registry.azurecr.io/trading-system:latest
      resources:
        requests:
          cpu: 2
          memoryInGb: 4
      ports:
      - port: 8000
      environmentVariables:
      - name: PYTHON_GIL
        value: '0'
  osType: Linux
  restartPolicy: Always
tags: null
type: Microsoft.ContainerInstance/containerGroups
```

#### Google Cloud部署

使用Cloud Run：

```yaml
# cloudrun.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: trading-system
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/cpu-throttling: "false"
    spec:
      containerConcurrency: 1000
      containers:
      - image: gcr.io/project-id/trading-system:latest
        ports:
        - containerPort: 8000
        env:
        - name: PYTHON_GIL
          value: "0"
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
```

## 配置管理

### 1. 环境特定配置

#### 开发环境 (`config/development.yaml`)

```yaml
system:
  logging:
    level: DEBUG
    file: logs/development.log
  
  performance:
    cache_size: 100
    max_workers: 2

data_sources:
  yahoo_finance:
    enabled: true
  binance:
    enabled: false
    use_testnet: true
```

#### 生产环境 (`config/production.yaml`)

```yaml
system:
  logging:
    level: INFO
    file: logs/production.log
  
  performance:
    cache_size: 10000
    max_workers: 16

data_sources:
  yahoo_finance:
    enabled: true
    rate_limit: 200
  binance:
    enabled: true
    use_testnet: false
```

### 2. 配置加载

```python
import os
from infrastructure.config import ConfigurationManager

# 根据环境加载配置
env = os.getenv('ENVIRONMENT', 'development')
config = ConfigurationManager(f"config/{env}")
```

## 监控和日志

### 1. 日志配置

```yaml
# config/logging.yaml
version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
  detailed:
    format: '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
  
  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/system.log
    maxBytes: 10485760  # 10MB
    backupCount: 5

loggers:
  '':
    level: DEBUG
    handlers: [console, file]
    propagate: false
```

### 2. 监控设置

#### Prometheus监控

```python
# monitoring/prometheus.py
from prometheus_client import Counter, Histogram, Gauge, start_http_server

# 定义指标
STRATEGY_EXECUTIONS = Counter('strategy_executions_total', 'Total strategy executions')
EXECUTION_TIME = Histogram('strategy_execution_seconds', 'Strategy execution time')
ACTIVE_POSITIONS = Gauge('active_positions', 'Number of active positions')

# 启动监控服务器
start_http_server(9090)
```

#### 健康检查

```python
# health_check.py
from fastapi import FastAPI
from fastapi.responses import JSONResponse

app = FastAPI()

@app.get("/health")
async def health_check():
    return JSONResponse({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    })

@app.get("/ready")
async def readiness_check():
    # 检查依赖服务
    checks = {
        "database": check_database(),
        "redis": check_redis(),
        "data_sources": check_data_sources()
    }
    
    all_ready = all(checks.values())
    status_code = 200 if all_ready else 503
    
    return JSONResponse(
        {"ready": all_ready, "checks": checks},
        status_code=status_code
    )
```

## 安全配置

### 1. 网络安全

```bash
# 防火墙配置
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 8000/tcp  # 应用端口
sudo ufw enable
```

### 2. 应用安全

```python
# security/config.py
import secrets
from cryptography.fernet import Fernet

class SecurityConfig:
    def __init__(self):
        self.secret_key = secrets.token_urlsafe(32)
        self.encryption_key = Fernet.generate_key()
        self.fernet = Fernet(self.encryption_key)
    
    def encrypt_sensitive_data(self, data: str) -> str:
        return self.fernet.encrypt(data.encode()).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        return self.fernet.decrypt(encrypted_data.encode()).decode()
```

## 备份和恢复

### 1. 数据备份

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
pg_dump trading > $BACKUP_DIR/database_$DATE.sql

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz config/

# 备份日志文件
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz logs/

# 清理旧备份 (保留30天)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 2. 自动备份

```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /opt/trading-system/scripts/backup.sh
```

## 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   # 增加swap空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

2. **端口占用**
   ```bash
   # 查找占用端口的进程
   sudo netstat -tulpn | grep :8000
   # 终止进程
   sudo kill -9 <PID>
   ```

3. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R trading:trading /opt/trading-system
   sudo chmod +x /opt/trading-system/main.py
   ```

### 日志分析

```bash
# 查看错误日志
grep ERROR logs/system.log | tail -20

# 监控实时日志
tail -f logs/system.log

# 分析性能日志
grep "execution_time" logs/system.log | awk '{print $NF}' | sort -n
```

通过遵循这个部署指南，您可以在各种环境中成功部署和运行量化交易系统。
