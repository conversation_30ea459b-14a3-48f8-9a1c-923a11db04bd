#!/usr/bin/env python3
"""
量化交易系统快速启动脚本

提供简化的启动方式。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 检查Python版本
if sys.version_info < (3, 13):
    print("警告: 推荐使用Python 3.13或更高版本以获得最佳性能")
    print(f"当前版本: {sys.version}")

def main():
    """主函数"""
    print("=" * 50)
    print("量化交易系统")
    print("=" * 50)
    
    try:
        # 导入并运行主程序
        from presentation.app import main as app_main
        app_main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖都已正确安装")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
