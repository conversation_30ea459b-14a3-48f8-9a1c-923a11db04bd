"""
仓储接口模块

定义领域层的仓储接口，用于数据持久化的抽象。
这些接口将由基础设施层实现。
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID

from .entities import Portfolio, Position, Transaction, Account
from .value_objects import MarketData, Signal, Order, PerformanceMetrics
from shared.constants import TimeFrame, OrderStatus, PositionStatus


class IMarketDataRepository(ABC):
    """市场数据仓储接口"""
    
    @abstractmethod
    async def save_market_data(self, data: MarketData) -> None:
        """保存市场数据"""
        pass
    
    @abstractmethod
    async def get_market_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame,
        start_time: datetime,
        end_time: datetime
    ) -> List[MarketData]:
        """获取历史市场数据"""
        pass
    
    @abstractmethod
    async def get_latest_market_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame
    ) -> Optional[MarketData]:
        """获取最新市场数据"""
        pass
    
    @abstractmethod
    async def save_batch_market_data(self, data_list: List[MarketData]) -> None:
        """批量保存市场数据"""
        pass
    
    @abstractmethod
    async def delete_market_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame,
        before_time: datetime
    ) -> int:
        """删除指定时间之前的市场数据"""
        pass
    
    @abstractmethod
    async def get_available_symbols(self) -> List[str]:
        """获取可用的交易品种列表"""
        pass
    
    @abstractmethod
    async def get_data_range(
        self, 
        symbol: str, 
        timeframe: TimeFrame
    ) -> Optional[tuple[datetime, datetime]]:
        """获取数据的时间范围"""
        pass


class IPortfolioRepository(ABC):
    """投资组合仓储接口"""
    
    @abstractmethod
    async def save_portfolio(self, portfolio: Portfolio) -> None:
        """保存投资组合"""
        pass
    
    @abstractmethod
    async def get_portfolio(self, portfolio_id: UUID) -> Optional[Portfolio]:
        """根据ID获取投资组合"""
        pass
    
    @abstractmethod
    async def get_portfolio_by_name(self, name: str) -> Optional[Portfolio]:
        """根据名称获取投资组合"""
        pass
    
    @abstractmethod
    async def get_all_portfolios(self) -> List[Portfolio]:
        """获取所有投资组合"""
        pass
    
    @abstractmethod
    async def delete_portfolio(self, portfolio_id: UUID) -> bool:
        """删除投资组合"""
        pass
    
    @abstractmethod
    async def save_position(self, position: Position) -> None:
        """保存持仓"""
        pass
    
    @abstractmethod
    async def get_position(self, position_id: UUID) -> Optional[Position]:
        """获取持仓"""
        pass
    
    @abstractmethod
    async def get_positions_by_portfolio(self, portfolio_id: UUID) -> List[Position]:
        """获取投资组合的所有持仓"""
        pass
    
    @abstractmethod
    async def get_positions_by_symbol(self, symbol: str) -> List[Position]:
        """获取指定品种的所有持仓"""
        pass
    
    @abstractmethod
    async def get_open_positions(self, portfolio_id: UUID) -> List[Position]:
        """获取投资组合的开仓持仓"""
        pass
    
    @abstractmethod
    async def update_position_status(
        self, 
        position_id: UUID, 
        status: PositionStatus
    ) -> bool:
        """更新持仓状态"""
        pass


class ITransactionRepository(ABC):
    """交易记录仓储接口"""
    
    @abstractmethod
    async def save_transaction(self, transaction: Transaction) -> None:
        """保存交易记录"""
        pass
    
    @abstractmethod
    async def get_transaction(self, transaction_id: UUID) -> Optional[Transaction]:
        """获取交易记录"""
        pass
    
    @abstractmethod
    async def get_transactions_by_portfolio(
        self, 
        portfolio_id: UUID,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Transaction]:
        """获取投资组合的交易记录"""
        pass
    
    @abstractmethod
    async def get_transactions_by_symbol(
        self, 
        symbol: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Transaction]:
        """获取指定品种的交易记录"""
        pass
    
    @abstractmethod
    async def get_transactions_by_strategy(
        self, 
        strategy_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Transaction]:
        """获取指定策略的交易记录"""
        pass
    
    @abstractmethod
    async def save_batch_transactions(self, transactions: List[Transaction]) -> None:
        """批量保存交易记录"""
        pass


class IOrderRepository(ABC):
    """订单仓储接口"""
    
    @abstractmethod
    async def save_order(self, order: Order) -> None:
        """保存订单"""
        pass
    
    @abstractmethod
    async def get_order(self, order_id: UUID) -> Optional[Order]:
        """获取订单"""
        pass
    
    @abstractmethod
    async def get_orders_by_status(self, status: OrderStatus) -> List[Order]:
        """根据状态获取订单"""
        pass
    
    @abstractmethod
    async def get_active_orders(self) -> List[Order]:
        """获取活跃订单"""
        pass
    
    @abstractmethod
    async def get_orders_by_symbol(self, symbol: str) -> List[Order]:
        """获取指定品种的订单"""
        pass
    
    @abstractmethod
    async def get_orders_by_strategy(self, strategy_id: str) -> List[Order]:
        """获取指定策略的订单"""
        pass
    
    @abstractmethod
    async def update_order_status(
        self, 
        order_id: UUID, 
        status: OrderStatus
    ) -> bool:
        """更新订单状态"""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: UUID) -> bool:
        """取消订单"""
        pass


class ISignalRepository(ABC):
    """信号仓储接口"""
    
    @abstractmethod
    async def save_signal(self, signal: Signal) -> None:
        """保存信号"""
        pass
    
    @abstractmethod
    async def get_signal(self, signal_id: UUID) -> Optional[Signal]:
        """获取信号"""
        pass
    
    @abstractmethod
    async def get_signals_by_strategy(
        self, 
        strategy_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Signal]:
        """获取指定策略的信号"""
        pass
    
    @abstractmethod
    async def get_signals_by_symbol(
        self, 
        symbol: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Signal]:
        """获取指定品种的信号"""
        pass
    
    @abstractmethod
    async def get_recent_signals(
        self, 
        limit: int = 100
    ) -> List[Signal]:
        """获取最近的信号"""
        pass
    
    @abstractmethod
    async def save_batch_signals(self, signals: List[Signal]) -> None:
        """批量保存信号"""
        pass


class IAccountRepository(ABC):
    """账户仓储接口"""
    
    @abstractmethod
    async def save_account(self, account: Account) -> None:
        """保存账户"""
        pass
    
    @abstractmethod
    async def get_account(self, account_id: UUID) -> Optional[Account]:
        """获取账户"""
        pass
    
    @abstractmethod
    async def get_account_by_name(self, name: str) -> Optional[Account]:
        """根据名称获取账户"""
        pass
    
    @abstractmethod
    async def get_all_accounts(self) -> List[Account]:
        """获取所有账户"""
        pass
    
    @abstractmethod
    async def update_account_balance(
        self, 
        account_id: UUID, 
        new_balance: float
    ) -> bool:
        """更新账户余额"""
        pass
    
    @abstractmethod
    async def delete_account(self, account_id: UUID) -> bool:
        """删除账户"""
        pass


class IStrategyRepository(ABC):
    """策略仓储接口"""
    
    @abstractmethod
    async def save_strategy_config(
        self, 
        strategy_id: str, 
        config: Dict[str, Any]
    ) -> None:
        """保存策略配置"""
        pass
    
    @abstractmethod
    async def get_strategy_config(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """获取策略配置"""
        pass
    
    @abstractmethod
    async def get_all_strategy_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有策略配置"""
        pass
    
    @abstractmethod
    async def delete_strategy_config(self, strategy_id: str) -> bool:
        """删除策略配置"""
        pass
    
    @abstractmethod
    async def save_strategy_performance(
        self, 
        strategy_id: str, 
        performance: PerformanceMetrics
    ) -> None:
        """保存策略性能指标"""
        pass
    
    @abstractmethod
    async def get_strategy_performance(
        self, 
        strategy_id: str
    ) -> Optional[PerformanceMetrics]:
        """获取策略性能指标"""
        pass


class IBacktestRepository(ABC):
    """回测仓储接口"""
    
    @abstractmethod
    async def save_backtest_result(
        self, 
        backtest_id: str,
        strategy_id: str,
        config: Dict[str, Any],
        performance: PerformanceMetrics,
        metadata: Dict[str, Any]
    ) -> None:
        """保存回测结果"""
        pass
    
    @abstractmethod
    async def get_backtest_result(self, backtest_id: str) -> Optional[Dict[str, Any]]:
        """获取回测结果"""
        pass
    
    @abstractmethod
    async def get_backtest_results_by_strategy(
        self, 
        strategy_id: str
    ) -> List[Dict[str, Any]]:
        """获取指定策略的回测结果"""
        pass
    
    @abstractmethod
    async def delete_backtest_result(self, backtest_id: str) -> bool:
        """删除回测结果"""
        pass
