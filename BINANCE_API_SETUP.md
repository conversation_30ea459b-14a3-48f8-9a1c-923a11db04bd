# Binance API 配置指南

本指南将帮助您正确配置 Binance API 密钥以获取实时行情数据。

## 1. 获取 Binance API 密钥

### 1.1 创建 Binance 账户
1. 访问 [Binance官网](https://www.binance.com)
2. 注册并完成身份验证

### 1.2 创建 API 密钥
1. 登录 Binance 账户
2. 进入 "API Management" 页面
3. 点击 "Create API"
4. 设置 API 密钥名称
5. 完成安全验证（邮箱、短信等）
6. **重要：仅启用 "Enable Reading" 权限**（用于获取市场数据）
7. 记录 API Key 和 Secret Key

### 1.3 测试网 API 密钥（推荐用于开发）
1. 访问 [Binance Testnet](https://testnet.binance.vision/)
2. 使用 GitHub 账户登录
3. 生成测试网 API 密钥
4. 测试网密钥可以安全用于开发和测试

## 2. 配置 API 密钥

### 方法一：使用环境变量（推荐）

#### Windows PowerShell:
```powershell
# 设置环境变量
$env:BINANCE_API_KEY = "your_api_key_here"
$env:BINANCE_SECRET_KEY = "your_secret_key_here"

# 永久设置（需要管理员权限）
[Environment]::SetEnvironmentVariable("BINANCE_API_KEY", "your_api_key_here", "User")
[Environment]::SetEnvironmentVariable("BINANCE_SECRET_KEY", "your_secret_key_here", "User")
```

#### Windows CMD:
```cmd
set BINANCE_API_KEY=your_api_key_here
set BINANCE_SECRET_KEY=your_secret_key_here
```

#### 创建 .env 文件:
在项目根目录创建 `.env` 文件：
```
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
```

### 方法二：直接修改配置文件
编辑 `config/data_sources.yaml` 文件：
```yaml
data_sources:
  binance:
    enabled: true
    api_key: "your_api_key_here"  # 直接填入API密钥
    secret_key: "your_secret_key_here"  # 直接填入Secret密钥
    use_testnet: true  # 生产环境设为 false
```

**⚠️ 安全警告：直接在配置文件中写入密钥存在安全风险，建议使用环境变量。**

## 3. 验证配置

### 3.1 检查配置
运行以下 Python 代码验证配置：
```python
import os
from infrastructure.config import ConfigurationManager

# 检查环境变量
api_key = os.getenv('BINANCE_API_KEY')
secret_key = os.getenv('BINANCE_SECRET_KEY')

print(f"API Key: {'已设置' if api_key else '未设置'}")
print(f"Secret Key: {'已设置' if secret_key else '未设置'}")

# 检查配置管理器
config = ConfigurationManager()
binance_config = config.get_config('data_sources.binance')
print(f"Binance 配置: {binance_config}")
```

### 3.2 测试连接
```python
from infrastructure.data_sources import BinanceDataSource
from infrastructure.config import ConfigurationManager

async def test_binance_connection():
    config = ConfigurationManager()
    binance = BinanceDataSource(config)
    
    try:
        # 测试获取交易对信息
        symbols = await binance.get_available_symbols()
        print(f"成功获取 {len(symbols)} 个交易对")
        
        # 测试获取实时数据
        data = await binance.get_realtime_data("BTCUSDT")
        print(f"BTCUSDT 实时价格: {data.close}")
        
    except Exception as e:
        print(f"连接失败: {e}")

# 运行测试
import asyncio
asyncio.run(test_binance_connection())
```

## 4. 常见问题

### 4.1 API 密钥权限错误
- **问题**: 403 Forbidden 错误
- **解决**: 确保 API 密钥有 "Enable Reading" 权限
- **注意**: 本项目只需要读取权限，不需要交易权限

### 4.2 IP 限制
- **问题**: IP 被限制访问
- **解决**: 在 Binance API 设置中添加当前 IP 到白名单
- **建议**: 测试网没有 IP 限制

### 4.3 速率限制
- **问题**: 429 Too Many Requests
- **解决**: 降低请求频率，当前配置为每分钟 1200 次
- **调整**: 修改 `config/data_sources.yaml` 中的 `rate_limit` 值

### 4.4 WebSocket 连接问题
- **问题**: WebSocket 连接失败或频繁断开
- **解决**: 检查网络连接，项目已实现自动重连机制
- **配置**: 可在 `realtime` 配置中调整重连参数

## 5. 安全最佳实践

1. **永远不要在代码中硬编码 API 密钥**
2. **使用环境变量或安全的密钥管理系统**
3. **定期轮换 API 密钥**
4. **仅授予必要的最小权限**
5. **监控 API 密钥使用情况**
6. **不要将包含密钥的配置文件提交到版本控制系统**

## 6. 生产环境配置

当准备部署到生产环境时：

1. 将 `use_testnet` 设置为 `false`
2. 使用生产环境的 API 密钥
3. 配置适当的 IP 白名单
4. 启用所有安全措施
5. 监控 API 使用量和错误率

```yaml
data_sources:
  binance:
    enabled: true
    api_key: "${BINANCE_API_KEY}"
    secret_key: "${BINANCE_SECRET_KEY}"
    use_testnet: false  # 生产环境
    rate_limit: 1200
```

## 7. 故障排除

如果遇到问题，请检查：

1. API 密钥是否正确设置
2. 网络连接是否正常
3. Binance 服务是否可用
4. 日志文件中的错误信息
5. 配置文件格式是否正确

查看日志文件：`logs/app.log`

---

**注意**: 本项目的 Binance 集成已经根据最新的 API 文档进行了优化，包括：
- 正确的 WebSocket 流格式
- 自动重连机制
- 错误处理和日志记录
- 速率限制管理