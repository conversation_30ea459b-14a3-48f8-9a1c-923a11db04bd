"""
异常定义模块

定义系统中使用的所有自定义异常类，提供清晰的错误分类和处理机制。
利用Python 3.13的改进错误消息特性提供更好的调试体验。
"""

from typing import Any, Dict, Optional


class QuantTradingException(Exception):
    """
    量化交易系统基础异常类
    
    所有系统自定义异常的基类，提供统一的异常处理接口。
    """
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}
    
    def __str__(self) -> str:
        base_msg = self.message
        if self.error_code:
            base_msg = f"[{self.error_code}] {base_msg}"
        if self.context:
            context_str = ", ".join(f"{k}={v}" for k, v in self.context.items())
            base_msg = f"{base_msg} (Context: {context_str})"
        return base_msg


class DataSourceException(QuantTradingException):
    """数据源相关异常"""
    
    def __init__(
        self, 
        message: str, 
        source_name: Optional[str] = None,
        **kwargs
    ):
        if source_name:
            kwargs.setdefault("context", {})["source"] = source_name
        super().__init__(message, **kwargs)


class DataValidationException(DataSourceException):
    """数据验证异常"""
    pass


class DataConnectionException(DataSourceException):
    """数据连接异常"""
    pass


class DataRateLimitException(DataSourceException):
    """数据源限流异常"""
    
    def __init__(
        self, 
        message: str, 
        retry_after: Optional[int] = None,
        **kwargs
    ):
        if retry_after:
            kwargs.setdefault("context", {})["retry_after"] = retry_after
        super().__init__(message, **kwargs)


class StrategyException(QuantTradingException):
    """策略相关异常"""
    
    def __init__(
        self, 
        message: str, 
        strategy_name: Optional[str] = None,
        **kwargs
    ):
        if strategy_name:
            kwargs.setdefault("context", {})["strategy"] = strategy_name
        super().__init__(message, **kwargs)


class StrategyLoadException(StrategyException):
    """策略加载异常"""
    pass


class StrategyExecutionException(StrategyException):
    """策略执行异常"""
    pass


class StrategyValidationException(StrategyException):
    """策略验证异常"""
    pass


class RiskManagementException(QuantTradingException):
    """风险管理异常"""
    
    def __init__(
        self, 
        message: str, 
        risk_type: Optional[str] = None,
        current_value: Optional[float] = None,
        threshold: Optional[float] = None,
        **kwargs
    ):
        context = kwargs.setdefault("context", {})
        if risk_type:
            context["risk_type"] = risk_type
        if current_value is not None:
            context["current_value"] = current_value
        if threshold is not None:
            context["threshold"] = threshold
        super().__init__(message, **kwargs)


class PositionSizeException(RiskManagementException):
    """仓位大小异常"""
    pass


class DrawdownException(RiskManagementException):
    """回撤异常"""
    pass


class VaRException(RiskManagementException):
    """风险价值异常"""
    pass


class ConfigurationException(QuantTradingException):
    """配置相关异常"""
    
    def __init__(
        self, 
        message: str, 
        config_key: Optional[str] = None,
        config_file: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.setdefault("context", {})
        if config_key:
            context["config_key"] = config_key
        if config_file:
            context["config_file"] = config_file
        super().__init__(message, **kwargs)


class ConfigurationMissingException(ConfigurationException):
    """配置缺失异常"""
    pass


class ConfigurationValidationException(ConfigurationException):
    """配置验证异常"""
    pass


class ValidationException(QuantTradingException):
    """通用验证异常"""
    
    def __init__(
        self, 
        message: str, 
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        **kwargs
    ):
        context = kwargs.setdefault("context", {})
        if field_name:
            context["field"] = field_name
        if field_value is not None:
            context["value"] = field_value
        super().__init__(message, **kwargs)


class BacktestException(QuantTradingException):
    """回测相关异常"""
    
    def __init__(
        self, 
        message: str, 
        backtest_id: Optional[str] = None,
        **kwargs
    ):
        if backtest_id:
            kwargs.setdefault("context", {})["backtest_id"] = backtest_id
        super().__init__(message, **kwargs)


class BacktestDataException(BacktestException):
    """回测数据异常"""
    pass


class BacktestExecutionException(BacktestException):
    """回测执行异常"""
    pass


class PortfolioException(QuantTradingException):
    """投资组合异常"""
    
    def __init__(
        self, 
        message: str, 
        portfolio_id: Optional[str] = None,
        **kwargs
    ):
        if portfolio_id:
            kwargs.setdefault("context", {})["portfolio_id"] = portfolio_id
        super().__init__(message, **kwargs)


class InsufficientFundsException(PortfolioException):
    """资金不足异常"""
    pass


class InvalidPositionException(PortfolioException):
    """无效仓位异常"""
    pass


class OrderException(QuantTradingException):
    """订单相关异常"""
    
    def __init__(
        self, 
        message: str, 
        order_id: Optional[str] = None,
        symbol: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.setdefault("context", {})
        if order_id:
            context["order_id"] = order_id
        if symbol:
            context["symbol"] = symbol
        super().__init__(message, **kwargs)


class OrderValidationException(OrderException):
    """订单验证异常"""
    pass


class OrderExecutionException(OrderException):
    """订单执行异常"""
    pass


class SignalException(QuantTradingException):
    """信号相关异常"""
    
    def __init__(
        self, 
        message: str, 
        signal_id: Optional[str] = None,
        signal_type: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.setdefault("context", {})
        if signal_id:
            context["signal_id"] = signal_id
        if signal_type:
            context["signal_type"] = signal_type
        super().__init__(message, **kwargs)


class InvalidSignalException(SignalException):
    """无效信号异常"""
    pass


class SignalProcessingException(SignalException):
    """信号处理异常"""
    pass


class SystemException(QuantTradingException):
    """系统级异常"""
    pass


class InitializationException(SystemException):
    """初始化异常"""
    pass


class ShutdownException(SystemException):
    """关闭异常"""
    pass


class ConcurrencyException(SystemException):
    """并发异常"""
    pass


# 异常映射字典，用于错误代码到异常类的映射
EXCEPTION_MAP = {
    "DATA_SOURCE": DataSourceException,
    "DATA_VALIDATION": DataValidationException,
    "DATA_CONNECTION": DataConnectionException,
    "DATA_RATE_LIMIT": DataRateLimitException,
    "STRATEGY": StrategyException,
    "STRATEGY_LOAD": StrategyLoadException,
    "STRATEGY_EXECUTION": StrategyExecutionException,
    "STRATEGY_VALIDATION": StrategyValidationException,
    "RISK_MANAGEMENT": RiskManagementException,
    "POSITION_SIZE": PositionSizeException,
    "DRAWDOWN": DrawdownException,
    "VAR": VaRException,
    "CONFIGURATION": ConfigurationException,
    "CONFIGURATION_MISSING": ConfigurationMissingException,
    "CONFIGURATION_VALIDATION": ConfigurationValidationException,
    "VALIDATION": ValidationException,
    "BACKTEST": BacktestException,
    "BACKTEST_DATA": BacktestDataException,
    "BACKTEST_EXECUTION": BacktestExecutionException,
    "PORTFOLIO": PortfolioException,
    "INSUFFICIENT_FUNDS": InsufficientFundsException,
    "INVALID_POSITION": InvalidPositionException,
    "ORDER": OrderException,
    "ORDER_VALIDATION": OrderValidationException,
    "ORDER_EXECUTION": OrderExecutionException,
    "SIGNAL": SignalException,
    "INVALID_SIGNAL": InvalidSignalException,
    "SIGNAL_PROCESSING": SignalProcessingException,
    "SYSTEM": SystemException,
    "INITIALIZATION": InitializationException,
    "SHUTDOWN": ShutdownException,
    "CONCURRENCY": ConcurrencyException,
}


def create_exception(error_code: str, message: str, **kwargs) -> QuantTradingException:
    """
    根据错误代码创建相应的异常实例
    
    Args:
        error_code: 错误代码
        message: 错误消息
        **kwargs: 其他参数
        
    Returns:
        相应的异常实例
    """
    exception_class = EXCEPTION_MAP.get(error_code, QuantTradingException)
    return exception_class(message, error_code=error_code, **kwargs)
