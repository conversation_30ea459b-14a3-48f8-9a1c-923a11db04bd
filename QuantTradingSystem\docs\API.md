# API 文档

本文档描述了量化交易系统的主要API接口。

## 核心接口

### 策略接口 (IStrategy)

策略是系统的核心组件，所有交易策略都必须实现此接口。

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Any
from domain.value_objects import MarketData, Signal

class IStrategy(ABC):
    """策略接口"""
    
    @abstractmethod
    async def initialize(self, context: StrategyContext) -> None:
        """初始化策略"""
        pass
    
    @abstractmethod
    async def on_data(self, data: MarketData) -> List[Signal]:
        """处理市场数据并生成信号"""
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数"""
        pass
    
    @abstractmethod
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """验证策略参数"""
        pass
```

#### 使用示例

```python
class MyStrategy(IStrategy):
    def __init__(self, strategy_id: str, config: Dict[str, Any]):
        self.strategy_id = strategy_id
        self.config = config
        self.period = config.get('period', 20)
    
    async def initialize(self, context: StrategyContext) -> None:
        self.context = context
        self.prices = []
    
    async def on_data(self, data: MarketData) -> List[Signal]:
        self.prices.append(float(data.close.value))
        
        if len(self.prices) >= self.period:
            # 计算信号
            avg_price = sum(self.prices[-self.period:]) / self.period
            current_price = self.prices[-1]
            
            if current_price > avg_price * 1.02:
                return [Signal(
                    symbol=data.symbol,
                    signal_type=SignalType.BUY,
                    strength=0.8,
                    price=data.close,
                    timestamp=data.timestamp,
                    strategy_id=self.strategy_id
                )]
        
        return []
    
    def get_parameters(self) -> Dict[str, Any]:
        return {'period': self.period}
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        period = parameters.get('period', 0)
        return isinstance(period, int) and 1 <= period <= 100
```

### 数据源接口 (IDataSource)

数据源接口定义了获取市场数据的标准方法。

```python
class IDataSource(ABC):
    """数据源接口"""
    
    @abstractmethod
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame,
        start_date: datetime, 
        end_date: datetime
    ) -> List[MarketData]:
        """获取历史数据"""
        pass
    
    @abstractmethod
    async def get_realtime_data(self, symbol: str) -> MarketData:
        """获取实时数据"""
        pass
    
    @abstractmethod
    async def subscribe_realtime_data(
        self, 
        symbols: List[str], 
        callback: Callable[[MarketData], None]
    ) -> str:
        """订阅实时数据"""
        pass
    
    @abstractmethod
    def unsubscribe_realtime_data(self, subscription_id: str) -> bool:
        """取消订阅"""
        pass
```

## 应用服务API

### DataService

数据服务提供统一的数据访问接口。

```python
class DataService:
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame,
        start_date: datetime, 
        end_date: datetime,
        source: Optional[str] = None
    ) -> List[MarketData]:
        """获取历史数据"""
        
    async def get_realtime_data(
        self, 
        symbol: str, 
        source: Optional[str] = None
    ) -> MarketData:
        """获取实时数据"""
        
    async def subscribe_realtime_data(
        self, 
        symbols: List[str], 
        callback: Callable[[MarketData], None],
        source: Optional[str] = None
    ) -> str:
        """订阅实时数据"""
```

#### 使用示例

```python
# 初始化数据服务
data_service = DataService(config)
await data_service.start()

# 获取历史数据
historical_data = await data_service.get_historical_data(
    symbol='AAPL',
    timeframe=TimeFrame.D1,
    start_date=datetime(2023, 1, 1),
    end_date=datetime(2023, 12, 31)
)

# 订阅实时数据
def on_data(data: MarketData):
    print(f"收到数据: {data.symbol} - {data.close}")

subscription_id = await data_service.subscribe_realtime_data(
    symbols=['AAPL', 'GOOGL'],
    callback=on_data
)
```

### StrategyService

策略服务管理策略的生命周期。

```python
class StrategyService:
    async def load_strategy(
        self, 
        strategy_id: str, 
        config: Optional[Dict[str, Any]] = None
    ) -> None:
        """加载策略"""
        
    async def unload_strategy(self, strategy_id: str) -> bool:
        """卸载策略"""
        
    async def execute_strategies(self, market_data: MarketData) -> None:
        """执行所有策略"""
        
    def get_strategy_info(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """获取策略信息"""
        
    def get_all_strategies(self) -> Dict[str, Dict[str, Any]]:
        """获取所有策略信息"""
```

#### 使用示例

```python
# 初始化策略服务
strategy_service = StrategyService(config, data_service)
await strategy_service.start()

# 加载策略
await strategy_service.load_strategy('sma_cross', {
    'fast_period': 10,
    'slow_period': 30
})

# 获取策略信息
strategy_info = strategy_service.get_strategy_info('sma_cross')
print(f"策略状态: {strategy_info['enabled']}")
```

### BacktestService

回测服务提供历史数据回测功能。

```python
class BacktestService:
    async def run_backtest(
        self, 
        strategy_name: str,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        initial_capital: float = 100000.0,
        config_override: Optional[Dict[str, Any]] = None,
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> BacktestResult:
        """运行回测"""
        
    async def optimize_parameters(
        self, 
        strategy_name: str,
        symbol: str,
        parameters: Dict[str, Any],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        method: str = "grid_search",
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> OptimizationResult:
        """优化参数"""
```

#### 使用示例

```python
# 运行回测
result = await backtest_service.run_backtest(
    strategy_name='sma_cross',
    symbol='AAPL',
    start_date=datetime(2023, 1, 1),
    end_date=datetime(2023, 12, 31),
    initial_capital=100000.0
)

print(f"总收益率: {result.performance.total_return:.2%}")
print(f"夏普比率: {result.performance.sharpe_ratio:.3f}")

# 参数优化
optimization_result = await backtest_service.optimize_parameters(
    strategy_name='sma_cross',
    symbol='AAPL',
    parameters={
        'fast_period': {'min': 5, 'max': 20, 'step': 1},
        'slow_period': {'min': 20, 'max': 50, 'step': 5}
    },
    method='grid_search'
)

print(f"最优参数: {optimization_result.best_parameters}")
```

## 领域对象API

### MarketData

市场数据值对象。

```python
@dataclass(frozen=True)
class MarketData:
    symbol: str
    timestamp: datetime
    open: Price
    high: Price
    low: Price
    close: Price
    volume: Volume
    timeframe: TimeFrame
    asset_type: AssetType
    metadata: Dict[str, Any] = field(default_factory=dict)
```

### Signal

交易信号值对象。

```python
@dataclass(frozen=True)
class Signal:
    symbol: str
    signal_type: SignalType
    strength: float
    timestamp: datetime
    strategy_id: str
    price: Optional[Price] = None
    volume: Optional[Volume] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
```

### Portfolio

投资组合实体。

```python
class Portfolio:
    def __init__(self, name: str, account: Account):
        self.id = uuid4()
        self.name = name
        self.account = account
        self.positions: Dict[str, Position] = {}
        self.transactions: List[Transaction] = []
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def add_position(self, position: Position) -> None:
        """添加持仓"""
        
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取持仓"""
        
    def get_open_positions(self) -> List[Position]:
        """获取开放持仓"""
        
    @property
    def total_value(self) -> Price:
        """总价值"""
        
    @property
    def total_market_value(self) -> Price:
        """市场价值"""
```

## 配置API

### ConfigurationManager

配置管理器提供统一的配置访问接口。

```python
class ConfigurationManager:
    def __init__(self, config_dir: str = "config"):
        """初始化配置管理器"""
        
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        
    def set_config(self, key: str, value: Any) -> None:
        """设置配置值"""
        
    def reload_config(self) -> None:
        """重新加载配置"""
        
    def save_config_to_file(self, filename: str) -> None:
        """保存配置到文件"""
```

#### 使用示例

```python
# 初始化配置管理器
config = ConfigurationManager("config")

# 获取配置
log_level = config.get_config("system.logging.level", "INFO")
strategy_config = config.get_config("strategies.default.sma_cross", {})

# 设置配置
config.set_config("system.performance.max_workers", 8)

# 保存配置
config.save_config_to_file("config/custom.yaml")
```

## 事件系统API

### SignalBus

信号总线用于策略间通信。

```python
class SignalBus:
    def publish_signal(self, signal: Signal) -> None:
        """发布信号"""
        
    def subscribe_signals(
        self, 
        callback: Callable[[Signal], None],
        filter_func: Optional[Callable[[Signal], bool]] = None
    ) -> str:
        """订阅信号"""
        
    def unsubscribe_signals(self, subscription_id: str) -> bool:
        """取消订阅"""
```

#### 使用示例

```python
# 创建信号总线
signal_bus = SignalBus()

# 订阅信号
def on_signal(signal: Signal):
    if signal.signal_type == SignalType.BUY:
        print(f"买入信号: {signal.symbol}")

subscription_id = signal_bus.subscribe_signals(
    callback=on_signal,
    filter_func=lambda s: s.strength > 0.7
)

# 发布信号
signal = Signal(
    symbol='AAPL',
    signal_type=SignalType.BUY,
    strength=0.8,
    timestamp=datetime.now(),
    strategy_id='my_strategy'
)
signal_bus.publish_signal(signal)
```

## 错误处理

### 异常类型

系统定义了多种异常类型：

```python
class QuantTradingException(Exception):
    """基础异常类"""
    pass

class StrategyException(QuantTradingException):
    """策略相关异常"""
    pass

class DataSourceException(QuantTradingException):
    """数据源异常"""
    pass

class BacktestException(QuantTradingException):
    """回测异常"""
    pass

class PortfolioException(QuantTradingException):
    """投资组合异常"""
    pass
```

### 错误处理示例

```python
try:
    await strategy_service.load_strategy('invalid_strategy')
except StrategyException as e:
    logger.error(f"策略加载失败: {e}")
except Exception as e:
    logger.error(f"未知错误: {e}")
```

## 日志API

### Logger

统一的日志接口。

```python
class Logger:
    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        """获取日志记录器"""
        
    @staticmethod
    def initialize(
        level: int = logging.INFO,
        file_path: Optional[str] = None,
        format: Optional[str] = None,
        max_file_size: int = 10 * 1024 * 1024,
        backup_count: int = 5
    ) -> None:
        """初始化日志系统"""
```

#### 使用示例

```python
# 获取日志记录器
logger = Logger.get_logger("my_module")

# 记录日志
logger.info("系统启动")
logger.warning("配置文件未找到，使用默认配置")
logger.error("数据库连接失败", exc_info=True)
```

## 性能监控API

### PerformanceMonitor

性能监控接口。

```python
class PerformanceMonitor:
    def start_timer(self, name: str) -> None:
        """开始计时"""
        
    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时"""
        
    def record_metric(self, name: str, value: float) -> None:
        """记录指标"""
        
    def get_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
```

#### 使用示例

```python
# 性能监控
monitor = PerformanceMonitor()

# 计时
monitor.start_timer("strategy_execution")
await strategy.execute()
execution_time = monitor.end_timer("strategy_execution")

# 记录指标
monitor.record_metric("memory_usage", psutil.virtual_memory().percent)

# 获取指标
metrics = monitor.get_metrics()
print(f"执行时间: {metrics['strategy_execution']:.4f}秒")
```

这些API提供了系统的核心功能接口，开发者可以基于这些接口构建自定义的策略、数据源和其他组件。
