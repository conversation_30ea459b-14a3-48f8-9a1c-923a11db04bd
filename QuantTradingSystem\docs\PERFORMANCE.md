# 性能优化指南

本文档介绍量化交易系统的性能优化策略和Python 3.13新特性的使用。

## Python 3.13 新特性

### 自由线程模式 (Free-threaded Mode)

Python 3.13引入了实验性的自由线程模式，可以显著提升多线程性能。

#### 启用自由线程模式

```bash
# 方法1: 使用命令行参数
python -X gil=0 main.py

# 方法2: 设置环境变量
export PYTHON_GIL=0
python main.py

# 方法3: 在代码中检测
import sys
if hasattr(sys, '_is_gil_enabled') and not sys._is_gil_enabled():
    print("自由线程模式已启用")
```

#### 系统中的应用

```python
# shared/utils/performance.py
import sys
import threading
from concurrent.futures import ThreadPoolExecutor

class PerformanceUtils:
    @staticmethod
    def is_free_threaded():
        """检查是否启用了自由线程模式"""
        return hasattr(sys, '_is_gil_enabled') and not sys._is_gil_enabled()
    
    @staticmethod
    def get_optimal_worker_count():
        """获取最优工作线程数"""
        if PerformanceUtils.is_free_threaded():
            # 自由线程模式下可以使用更多线程
            return min(32, (os.cpu_count() or 1) * 4)
        else:
            # 传统GIL模式下限制线程数
            return min(8, (os.cpu_count() or 1) + 4)
```

### 改进的错误消息

Python 3.13提供了更详细的错误信息，有助于调试：

```python
# 利用改进的错误消息进行更好的异常处理
try:
    # 策略执行代码
    result = await strategy.execute()
except Exception as e:
    # Python 3.13提供更详细的堆栈信息
    logger.error(f"策略执行失败: {e}", exc_info=True)
```

### 新的类型系统特性

```python
# 使用新的类型注解特性
from typing import override

class EnhancedStrategy(BaseStrategy):
    @override
    def calculate_signals(self, data: MarketData) -> list[Signal]:
        # 实现具体策略逻辑
        pass
```

## 性能优化策略

### 1. 异步编程优化

#### 事件循环优化

```python
import asyncio
import uvloop  # 可选的高性能事件循环

# 使用高性能事件循环
if sys.platform != 'win32':
    asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

# 优化的异步任务管理
class AsyncTaskManager:
    def __init__(self, max_concurrent_tasks=100):
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.tasks = set()
    
    async def run_task(self, coro):
        async with self.semaphore:
            task = asyncio.create_task(coro)
            self.tasks.add(task)
            try:
                return await task
            finally:
                self.tasks.discard(task)
```

#### 批量处理优化

```python
class BatchProcessor:
    def __init__(self, batch_size=100, flush_interval=1.0):
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.buffer = []
        self.last_flush = time.time()
    
    async def add_item(self, item):
        self.buffer.append(item)
        
        if (len(self.buffer) >= self.batch_size or 
            time.time() - self.last_flush >= self.flush_interval):
            await self.flush()
    
    async def flush(self):
        if self.buffer:
            await self.process_batch(self.buffer.copy())
            self.buffer.clear()
            self.last_flush = time.time()
```

### 2. 内存优化

#### 对象池模式

```python
from queue import Queue
from typing import TypeVar, Generic

T = TypeVar('T')

class ObjectPool(Generic[T]):
    def __init__(self, factory, max_size=100):
        self.factory = factory
        self.pool = Queue(maxsize=max_size)
        self.max_size = max_size
    
    def get(self) -> T:
        try:
            return self.pool.get_nowait()
        except:
            return self.factory()
    
    def put(self, obj: T):
        try:
            self.pool.put_nowait(obj)
        except:
            pass  # 池已满，丢弃对象

# 使用示例
market_data_pool = ObjectPool(lambda: MarketData(), max_size=1000)
```

#### 内存映射文件

```python
import mmap
import pickle

class MemoryMappedCache:
    def __init__(self, filename, size=1024*1024*100):  # 100MB
        self.filename = filename
        self.size = size
        self.file = open(filename, 'r+b')
        self.mmap = mmap.mmap(self.file.fileno(), size)
    
    def store(self, key: str, data: any):
        serialized = pickle.dumps(data)
        # 存储逻辑
    
    def load(self, key: str):
        # 加载逻辑
        pass
```

### 3. 数据结构优化

#### 使用NumPy数组

```python
import numpy as np
from numba import jit

class OptimizedIndicators:
    @staticmethod
    @jit(nopython=True)
    def sma(prices: np.ndarray, period: int) -> np.ndarray:
        """优化的简单移动平均计算"""
        result = np.empty_like(prices)
        result[:period-1] = np.nan
        
        for i in range(period-1, len(prices)):
            result[i] = np.mean(prices[i-period+1:i+1])
        
        return result
    
    @staticmethod
    @jit(nopython=True)
    def rsi(prices: np.ndarray, period: int = 14) -> np.ndarray:
        """优化的RSI计算"""
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gains = np.empty_like(prices)
        avg_losses = np.empty_like(prices)
        
        # 初始平均值
        avg_gains[period] = np.mean(gains[:period])
        avg_losses[period] = np.mean(losses[:period])
        
        # 指数移动平均
        for i in range(period + 1, len(prices)):
            avg_gains[i] = (avg_gains[i-1] * (period-1) + gains[i-1]) / period
            avg_losses[i] = (avg_losses[i-1] * (period-1) + losses[i-1]) / period
        
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
```

### 4. 缓存优化

#### 多级缓存系统

```python
from functools import lru_cache
import redis
import pickle

class MultiLevelCache:
    def __init__(self, redis_client=None):
        self.redis_client = redis_client
        self.local_cache = {}
        self.max_local_size = 1000
    
    @lru_cache(maxsize=128)
    def get_from_memory(self, key: str):
        """内存缓存"""
        return self.local_cache.get(key)
    
    async def get(self, key: str):
        # L1: 内存缓存
        value = self.get_from_memory(key)
        if value is not None:
            return value
        
        # L2: Redis缓存
        if self.redis_client:
            try:
                data = await self.redis_client.get(key)
                if data:
                    value = pickle.loads(data)
                    self.set_local(key, value)
                    return value
            except:
                pass
        
        return None
    
    def set_local(self, key: str, value):
        if len(self.local_cache) >= self.max_local_size:
            # 简单的LRU清理
            oldest_key = next(iter(self.local_cache))
            del self.local_cache[oldest_key]
        
        self.local_cache[key] = value
```

### 5. 数据库优化

#### 连接池管理

```python
import asyncpg
import asyncio

class DatabasePool:
    def __init__(self, dsn: str, min_size=10, max_size=20):
        self.dsn = dsn
        self.min_size = min_size
        self.max_size = max_size
        self.pool = None
    
    async def initialize(self):
        self.pool = await asyncpg.create_pool(
            self.dsn,
            min_size=self.min_size,
            max_size=self.max_size,
            command_timeout=60
        )
    
    async def execute_batch(self, query: str, data: list):
        async with self.pool.acquire() as conn:
            await conn.executemany(query, data)
```

## 性能监控

### 1. 性能指标收集

```python
import time
import psutil
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class PerformanceMetrics:
    cpu_usage: float
    memory_usage: float
    disk_io: Dict[str, Any]
    network_io: Dict[str, Any]
    execution_time: float
    cache_hit_rate: float

class PerformanceMonitor:
    def __init__(self):
        self.metrics_history = []
        self.start_time = time.time()
    
    def collect_metrics(self) -> PerformanceMetrics:
        process = psutil.Process()
        
        return PerformanceMetrics(
            cpu_usage=process.cpu_percent(),
            memory_usage=process.memory_info().rss / 1024 / 1024,  # MB
            disk_io=process.io_counters()._asdict(),
            network_io=psutil.net_io_counters()._asdict(),
            execution_time=time.time() - self.start_time,
            cache_hit_rate=self.calculate_cache_hit_rate()
        )
    
    def calculate_cache_hit_rate(self) -> float:
        # 实现缓存命中率计算
        return 0.0
```

### 2. 性能分析工具

```python
import cProfile
import pstats
from functools import wraps

def profile_performance(func):
    """性能分析装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        profiler = cProfile.Profile()
        profiler.enable()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            profiler.disable()
            stats = pstats.Stats(profiler)
            stats.sort_stats('cumulative')
            stats.print_stats(10)  # 显示前10个最耗时的函数
    
    return wrapper

# 异步版本
def profile_async_performance(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            execution_time = time.time() - start_time
            print(f"{func.__name__} 执行时间: {execution_time:.4f}秒")
    
    return wrapper
```

## 配置优化

### 系统配置优化

```yaml
# config/performance.yaml
performance:
  # 线程配置
  threading:
    max_workers: 16
    use_free_threading: true
    
  # 缓存配置
  cache:
    memory_cache_size: 1000
    redis_cache_ttl: 3600
    enable_compression: true
    
  # 数据库配置
  database:
    connection_pool_size: 20
    connection_timeout: 30
    query_timeout: 60
    
  # 异步配置
  async:
    max_concurrent_tasks: 100
    batch_size: 50
    flush_interval: 1.0
    
  # 监控配置
  monitoring:
    enable_profiling: false
    metrics_interval: 60
    log_slow_queries: true
    slow_query_threshold: 1.0
```

### 环境变量配置

```bash
# .env
# Python优化
PYTHONOPTIMIZE=2
PYTHONUNBUFFERED=1

# 自由线程模式
PYTHON_GIL=0

# 内存优化
MALLOC_ARENA_MAX=4

# 异步优化
ASYNCIO_DEBUG=0
```

## 性能测试

### 基准测试

```python
import asyncio
import time
from typing import Callable, Any

class BenchmarkSuite:
    def __init__(self):
        self.results = {}
    
    def benchmark(self, name: str, iterations: int = 1000):
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                times = []
                for _ in range(iterations):
                    start = time.perf_counter()
                    result = func(*args, **kwargs)
                    end = time.perf_counter()
                    times.append(end - start)
                
                self.results[name] = {
                    'avg_time': sum(times) / len(times),
                    'min_time': min(times),
                    'max_time': max(times),
                    'total_time': sum(times)
                }
                
                return result
            return wrapper
        return decorator
    
    async def async_benchmark(self, name: str, iterations: int = 1000):
        def decorator(func: Callable):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                times = []
                for _ in range(iterations):
                    start = time.perf_counter()
                    result = await func(*args, **kwargs)
                    end = time.perf_counter()
                    times.append(end - start)
                
                self.results[name] = {
                    'avg_time': sum(times) / len(times),
                    'min_time': min(times),
                    'max_time': max(times),
                    'total_time': sum(times)
                }
                
                return result
            return wrapper
        return decorator
    
    def print_results(self):
        for name, metrics in self.results.items():
            print(f"{name}:")
            print(f"  平均时间: {metrics['avg_time']:.6f}秒")
            print(f"  最小时间: {metrics['min_time']:.6f}秒")
            print(f"  最大时间: {metrics['max_time']:.6f}秒")
            print(f"  总时间: {metrics['total_time']:.6f}秒")
```

## 最佳实践

1. **使用Python 3.13自由线程模式**进行CPU密集型任务
2. **合理使用异步编程**避免阻塞操作
3. **实施多级缓存策略**减少重复计算
4. **使用NumPy和Numba**优化数值计算
5. **监控系统性能**及时发现瓶颈
6. **定期进行性能测试**确保系统稳定性

## 故障排除

### 常见性能问题

1. **内存泄漏**: 使用内存分析工具检查
2. **CPU使用率过高**: 检查算法复杂度
3. **I/O阻塞**: 使用异步I/O操作
4. **缓存失效**: 优化缓存策略
5. **数据库查询慢**: 优化SQL查询和索引

### 调试工具

```python
# 内存使用分析
import tracemalloc

tracemalloc.start()
# ... 运行代码 ...
current, peak = tracemalloc.get_traced_memory()
print(f"当前内存使用: {current / 1024 / 1024:.1f} MB")
print(f"峰值内存使用: {peak / 1024 / 1024:.1f} MB")
tracemalloc.stop()

# CPU分析
import cProfile
cProfile.run('your_function()', 'profile_output.prof')
```

通过这些优化策略，系统可以充分利用Python 3.13的新特性，实现更高的性能和更好的用户体验。
