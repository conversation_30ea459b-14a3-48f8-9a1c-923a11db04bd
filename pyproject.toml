[build-system]
requires = ["setuptools>=68.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "quant-trading-system"
version = "1.0.0"
description = "个人量化交易系统 - 基于Python 3.13的现代化量化交易平台"
authors = [
    {name = "Augment Agent", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.13"
keywords = ["quantitative", "trading", "finance", "backtesting", "strategy"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.13",
    "Topic :: Office/Business :: Financial :: Investment",
    "Topic :: Scientific/Engineering :: Information Analysis",
]

dependencies = [
    # 数据处理和分析
    "pandas>=2.1.0",
    "numpy>=1.24.0",
    "scipy>=1.11.0",
    
    # 数据库
    "sqlalchemy>=2.0.0",
    "sqlite3",  # Python 3.13内置
    
    # 网络请求和API
    "aiohttp>=3.9.0",
    "requests>=2.31.0",
    "websockets>=12.0",
    
    # 配置管理
    "pydantic>=2.5.0",
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0",
    
    # 图表和可视化
    "plotly>=5.17.0",
    "matplotlib>=3.8.0",
    "seaborn>=0.13.0",
    
    # GUI框架
    "tkinter",  # Python 3.13内置
    "customtkinter>=5.2.0",
    
    # 技术指标计算
    "ta-lib>=0.4.28",
    "pandas-ta>=0.3.14b",
    
    # 机器学习（可选）
    "scikit-learn>=1.3.0",
    
    # 日志和监控
    "loguru>=0.7.0",
    
    # 测试
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    
    # 类型检查
    "mypy>=1.7.0",
    "types-requests",
    "types-PyYAML",
]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "pre-commit>=3.5.0",
]

ml = [
    "tensorflow>=2.15.0",
    "torch>=2.1.0",
    "xgboost>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/augment/quant-trading-system"
Repository = "https://github.com/augment/quant-trading-system"
Documentation = "https://quant-trading-system.readthedocs.io"

[project.scripts]
qts = "QuantTradingSystem.main:main"

[tool.setuptools.packages.find]
include = ["QuantTradingSystem*"]

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=QuantTradingSystem",
    "--cov-report=term-missing",
    "--cov-report=html",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["QuantTradingSystem"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
