"""
应用服务模块

应用层服务 - 协调领域层和基础设施层，实现业务流程编排。
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
import threading

from domain.value_objects import MarketData, Signal, Order, PerformanceMetrics
from domain.entities import Portfolio, Position, Transaction
from domain.services import PortfolioService, PerformanceCalculationService
from shared.constants import SignalType, OrderStatus, StrategyStatus, TimeFrame
from shared.exceptions import (
    StrategyException, BacktestException,
    DataSourceException, PortfolioException
)
from shared.utils import Logger, AsyncUtils
from infrastructure.config import ConfigurationManager
from infrastructure.data_manager import DataManager, DataRequest
from infrastructure.external import NotificationManager
from application.strategies import IStrategy, STRATEGY_REGISTRY
from application.strategy_engine import StrategyEngine, SignalBus
from application.backtest_engine import BacktestEngine, BacktestConfig, BacktestResult
from application.optimizer import ParameterOptimizer, OptimizationConfig, OptimizationResult
from application.risk_manager import RiskManager
from application.signal_processor import SignalProcessor


@dataclass
class ServiceStatus:
    """服务状态"""
    is_running: bool = False
    start_time: Optional[datetime] = None
    error_count: int = 0
    last_error: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class DataService:
    """
    数据服务

    统一的数据获取和管理服务，封装数据复杂性。
    """

    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("data_service")

        # 初始化数据管理器
        self.data_manager = DataManager(config)

        # 服务状态
        self.status = ServiceStatus()
        self._lock = threading.RLock()

    async def start(self) -> None:
        """启动数据服务"""
        with self._lock:
            if self.status.is_running:
                return

            self.status.is_running = True
            self.status.start_time = datetime.now()

        self.logger.info("数据服务已启动")

    async def stop(self) -> None:
        """停止数据服务"""
        with self._lock:
            if not self.status.is_running:
                return

            self.status.is_running = False

        # 清理数据管理器
        await self.data_manager.cleanup()

        self.logger.info("数据服务已停止")

    async def get_historical_data(
        self,
        symbol: str,
        timeframe: TimeFrame,
        start_date: datetime,
        end_date: datetime,
        source: Optional[str] = None
    ) -> List[MarketData]:
        """获取历史数据"""
        try:
            request = DataRequest(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                source=source
            )

            data_set = await self.data_manager.get_market_data(request)
            return data_set.data

        except Exception as e:
            self._handle_error(f"获取历史数据失败: {e}")
            raise DataSourceException(f"获取历史数据失败: {e}")

    async def get_realtime_data(self, symbol: str, source: Optional[str] = None) -> MarketData:
        """获取实时数据"""
        try:
            return await self.data_manager.get_realtime_data(symbol, source)
        except Exception as e:
            self._handle_error(f"获取实时数据失败: {e}")
            raise DataSourceException(f"获取实时数据失败: {e}")

    async def subscribe_realtime_data(
        self,
        symbols: List[str],
        callback: Callable[[MarketData], None],
        source: Optional[str] = None
    ) -> str:
        """订阅实时数据"""
        try:
            return await self.data_manager.subscribe_realtime_data(symbols, callback, source)
        except Exception as e:
            self._handle_error(f"订阅实时数据失败: {e}")
            raise DataSourceException(f"订阅实时数据失败: {e}")

    def unsubscribe_realtime_data(self, subscription_id: str) -> bool:
        """取消实时数据订阅"""
        return self.data_manager.unsubscribe_realtime_data(subscription_id)

    def get_available_symbols(self, source: Optional[str] = None) -> List[str]:
        """获取可用的交易品种"""
        return self.data_manager.get_available_symbols(source)

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return self.data_manager.get_cache_info()

    def clear_cache(self) -> None:
        """清空缓存"""
        self.data_manager.clear_cache()

    def get_status(self) -> ServiceStatus:
        """获取服务状态"""
        with self._lock:
            return self.status

    def _handle_error(self, error_message: str) -> None:
        """处理错误"""
        with self._lock:
            self.status.error_count += 1
            self.status.last_error = error_message

        self.logger.error(error_message)


class StrategyService:
    """
    策略服务

    策略管理和执行服务，封装策略复杂性。
    """

    def __init__(self, config: ConfigurationManager, data_service: DataService):
        self.config = config
        self.data_service = data_service
        self.logger = Logger.get_logger("strategy_service")

        # 初始化组件
        self.signal_bus = SignalBus()
        self.risk_manager = RiskManager(config)
        self.signal_processor = SignalProcessor(config, self.risk_manager)
        self.strategy_engine = StrategyEngine(config, self.signal_bus)

        # 服务状态
        self.status = ServiceStatus()
        self._lock = threading.RLock()

        # 订阅信号
        self.signal_bus.subscribe_signals(self._handle_signal)

    async def start(self) -> None:
        """启动策略服务"""
        with self._lock:
            if self.status.is_running:
                return

            self.status.is_running = True
            self.status.start_time = datetime.now()

        # 启动组件
        await self.strategy_engine.start()

        self.logger.info("策略服务已启动")

    async def stop(self) -> None:
        """停止策略服务"""
        with self._lock:
            if not self.status.is_running:
                return

            self.status.is_running = False

        # 停止组件
        await self.strategy_engine.stop()

        self.logger.info("策略服务已停止")

    async def load_strategy(self, strategy_id: str, config: Optional[Dict[str, Any]] = None) -> None:
        """加载策略"""
        try:
            # 获取策略类
            strategy_class = STRATEGY_REGISTRY.get(strategy_id)
            if not strategy_class:
                raise StrategyException(f"未知的策略: {strategy_id}")

            # 获取策略配置
            if config is None:
                config = self.config.get_config(f"strategies.default.{strategy_id}", {})

            # 注册策略
            await self.strategy_engine.register_strategy(strategy_id, strategy_class, config)

            self.logger.info(f"策略已加载: {strategy_id}")

        except Exception as e:
            self._handle_error(f"加载策略失败: {e}")
            raise StrategyException(f"加载策略失败: {e}", strategy_name=strategy_id)

    async def unload_strategy(self, strategy_id: str) -> bool:
        """卸载策略"""
        try:
            result = await self.strategy_engine.unregister_strategy(strategy_id)
            if result:
                self.logger.info(f"策略已卸载: {strategy_id}")
            return result
        except Exception as e:
            self._handle_error(f"卸载策略失败: {e}")
            return False

    async def execute_strategies(self, market_data: MarketData) -> None:
        """执行策略"""
        try:
            await self.strategy_engine.execute_strategies(market_data)
        except Exception as e:
            self._handle_error(f"执行策略失败: {e}")

    async def _handle_signal(self, signal: Signal) -> None:
        """处理信号"""
        try:
            # 这里应该有投资组合实例，简化处理
            # 在实际实现中，需要从投资组合服务获取
            portfolio = None  # TODO: 获取投资组合

            if portfolio:
                result = await self.signal_processor.process_signal(signal, portfolio)

                if result.generated_orders:
                    # 处理生成的订单
                    for order in result.generated_orders:
                        self.logger.info(f"生成订单: {order.symbol} {order.side.value} {order.quantity.value}")
                        # TODO: 发送订单到执行系统

        except Exception as e:
            self._handle_error(f"处理信号失败: {e}")

    def get_strategy_info(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """获取策略信息"""
        strategy_info = self.strategy_engine.get_strategy_info(strategy_id)
        if strategy_info:
            return {
                'strategy_id': strategy_info.strategy_id,
                'enabled': strategy_info.enabled,
                'config': strategy_info.config,
                'last_update': strategy_info.last_update,
                'metrics': strategy_info.instance.get_metrics() if strategy_info.instance else None
            }
        return None

    def get_all_strategies(self) -> Dict[str, Dict[str, Any]]:
        """获取所有策略信息"""
        strategies = {}
        for strategy_id, strategy_info in self.strategy_engine.get_all_strategies().items():
            strategies[strategy_id] = {
                'strategy_id': strategy_info.strategy_id,
                'enabled': strategy_info.enabled,
                'config': strategy_info.config,
                'last_update': strategy_info.last_update,
                'metrics': strategy_info.instance.get_metrics() if strategy_info.instance else None
            }
        return strategies

    def get_signal_processing_stats(self) -> Dict[str, Any]:
        """获取信号处理统计"""
        return self.signal_processor.get_processing_stats()

    def get_status(self) -> ServiceStatus:
        """获取服务状态"""
        with self._lock:
            return self.status

    def _handle_error(self, error_message: str) -> None:
        """处理错误"""
        with self._lock:
            self.status.error_count += 1
            self.status.last_error = error_message

        self.logger.error(error_message)


class BacktestService:
    """
    回测服务

    回测和优化服务，封装回测复杂性。
    """

    def __init__(self, config: ConfigurationManager, data_service: DataService):
        self.config = config
        self.data_service = data_service
        self.logger = Logger.get_logger("backtest_service")

        # 初始化组件
        self.backtest_engine = BacktestEngine(config, data_service.data_manager)
        self.parameter_optimizer = ParameterOptimizer(config, self.backtest_engine)

        # 服务状态
        self.status = ServiceStatus()
        self._lock = threading.RLock()

    async def start(self) -> None:
        """启动回测服务"""
        with self._lock:
            if self.status.is_running:
                return

            self.status.is_running = True
            self.status.start_time = datetime.now()

        self.logger.info("回测服务已启动")

    async def stop(self) -> None:
        """停止回测服务"""
        with self._lock:
            if not self.status.is_running:
                return

            self.status.is_running = False

        self.logger.info("回测服务已停止")

    async def run_backtest(
        self,
        strategy_name: str,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        initial_capital: float = 100000.0,
        config_override: Optional[Dict[str, Any]] = None,
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> BacktestResult:
        """运行回测"""
        try:
            # 获取策略类
            strategy_class = STRATEGY_REGISTRY.get(strategy_name)
            if not strategy_class:
                raise BacktestException(f"未知的策略: {strategy_name}")

            # 获取策略配置
            strategy_config = self.config.get_config(f"strategies.default.{strategy_name}", {})
            if config_override:
                strategy_config.update(config_override)

            # 创建策略实例
            strategy = strategy_class(strategy_name, strategy_config)

            # 设置默认日期
            if start_date is None:
                start_date = datetime.now() - timedelta(days=365)
            if end_date is None:
                end_date = datetime.now()

            # 创建回测配置
            backtest_config = BacktestConfig(
                strategy_id=strategy_name,
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital
            )

            # 运行回测
            result = await self.backtest_engine.run_backtest(
                strategy,
                backtest_config,
                progress_callback
            )

            self.logger.info(f"回测完成: {strategy_name}, 收益率: {result.performance.total_return:.2%}")

            return result

        except Exception as e:
            self._handle_error(f"回测失败: {e}")
            raise BacktestException(f"回测失败: {e}")

    async def optimize_parameters(
        self,
        strategy_name: str,
        symbol: str,
        parameters: Dict[str, Any],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        method: str = "grid_search",
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> OptimizationResult:
        """优化参数"""
        try:
            from .optimizer import ParameterRange, OptimizationMethod

            # 设置默认日期
            if start_date is None:
                start_date = datetime.now() - timedelta(days=365)
            if end_date is None:
                end_date = datetime.now()

            # 创建回测配置
            backtest_config = BacktestConfig(
                strategy_id=strategy_name,
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )

            # 创建参数范围
            param_ranges = []
            for param_name, param_config in parameters.items():
                if isinstance(param_config, dict):
                    param_range = ParameterRange(
                        name=param_name,
                        min_value=param_config.get('min', 0),
                        max_value=param_config.get('max', 100),
                        step=param_config.get('step', 1),
                        values=param_config.get('values', [])
                    )
                    param_ranges.append(param_range)

            # 创建优化配置
            optimization_config = OptimizationConfig(
                strategy_id=strategy_name,
                parameters=param_ranges,
                backtest_config=backtest_config,
                method=OptimizationMethod(method.upper())
            )

            # 运行优化
            result = await self.parameter_optimizer.optimize_parameters(
                optimization_config,
                progress_callback
            )

            self.logger.info(f"参数优化完成: {strategy_name}")

            return result

        except Exception as e:
            self._handle_error(f"参数优化失败: {e}")
            raise BacktestException(f"参数优化失败: {e}")

    def get_running_backtests(self) -> Dict[str, Any]:
        """获取正在运行的回测"""
        return self.backtest_engine.get_running_backtests()

    def get_running_optimizations(self) -> Dict[str, Any]:
        """获取正在运行的优化"""
        return self.parameter_optimizer.get_running_optimizations()

    def cancel_backtest(self, backtest_id: str) -> bool:
        """取消回测"""
        return self.backtest_engine.cancel_backtest(backtest_id)

    def cancel_optimization(self, optimization_id: str) -> bool:
        """取消优化"""
        return self.parameter_optimizer.cancel_optimization(optimization_id)

    def get_status(self) -> ServiceStatus:
        """获取服务状态"""
        with self._lock:
            return self.status

    def _handle_error(self, error_message: str) -> None:
        """处理错误"""
        with self._lock:
            self.status.error_count += 1
            self.status.last_error = error_message

        self.logger.error(error_message)


class PortfolioService:
    """
    投资组合服务

    投资组合管理服务，封装投资组合复杂性。
    """

    def __init__(self, config: ConfigurationManager):
        self.config = config
        self.logger = Logger.get_logger("portfolio_service")

        # 投资组合存储
        self._portfolios: Dict[str, Portfolio] = {}
        self._lock = threading.RLock()

        # 服务状态
        self.status = ServiceStatus()

    async def start(self) -> None:
        """启动投资组合服务"""
        with self._lock:
            if self.status.is_running:
                return

            self.status.is_running = True
            self.status.start_time = datetime.now()

        self.logger.info("投资组合服务已启动")

    async def stop(self) -> None:
        """停止投资组合服务"""
        with self._lock:
            if not self.status.is_running:
                return

            self.status.is_running = False

        self.logger.info("投资组合服务已停止")

    def create_portfolio(self, name: str, initial_capital: float) -> Portfolio:
        """创建投资组合"""
        try:
            from domain.entities import Account
            from domain.value_objects import Price
            from decimal import Decimal

            # 创建账户
            account = Account(
                name=f"{name}_account",
                initial_balance=Price(Decimal(str(initial_capital))),
                current_balance=Price(Decimal(str(initial_capital))),
                available_balance=Price(Decimal(str(initial_capital)))
            )

            # 创建投资组合
            portfolio = Portfolio(
                name=name,
                account=account
            )

            with self._lock:
                self._portfolios[str(portfolio.id)] = portfolio

            self.logger.info(f"投资组合已创建: {name}")

            return portfolio

        except Exception as e:
            self._handle_error(f"创建投资组合失败: {e}")
            raise PortfolioException(f"创建投资组合失败: {e}")

    def get_portfolio(self, portfolio_id: str) -> Optional[Portfolio]:
        """获取投资组合"""
        with self._lock:
            return self._portfolios.get(portfolio_id)

    def get_all_portfolios(self) -> Dict[str, Portfolio]:
        """获取所有投资组合"""
        with self._lock:
            return self._portfolios.copy()

    def delete_portfolio(self, portfolio_id: str) -> bool:
        """删除投资组合"""
        with self._lock:
            if portfolio_id in self._portfolios:
                del self._portfolios[portfolio_id]
                self.logger.info(f"投资组合已删除: {portfolio_id}")
                return True
            return False

    def calculate_portfolio_performance(self, portfolio_id: str) -> Optional[PerformanceMetrics]:
        """计算投资组合性能"""
        portfolio = self.get_portfolio(portfolio_id)
        if portfolio:
            return PerformanceCalculationService.calculate_portfolio_performance(portfolio)
        return None

    def get_portfolio_summary(self, portfolio_id: str) -> Optional[Dict[str, Any]]:
        """获取投资组合摘要"""
        portfolio = self.get_portfolio(portfolio_id)
        if not portfolio:
            return None

        performance = self.calculate_portfolio_performance(portfolio_id)

        return {
            'id': str(portfolio.id),
            'name': portfolio.name,
            'total_value': float(portfolio.total_value.value),
            'cash_balance': float(portfolio.account.current_balance.value),
            'positions_value': float(portfolio.total_market_value.value),
            'position_count': len(portfolio.get_open_positions()),
            'performance': performance.to_dict() if performance else {},
            'created_at': portfolio.created_at,
            'updated_at': portfolio.updated_at
        }

    def get_status(self) -> ServiceStatus:
        """获取服务状态"""
        with self._lock:
            return self.status

    def _handle_error(self, error_message: str) -> None:
        """处理错误"""
        with self._lock:
            self.status.error_count += 1
            self.status.last_error = error_message

        self.logger.error(error_message)