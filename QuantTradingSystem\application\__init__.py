"""
应用层模块 (Application Layer Module)

协调领域层和基础设施层，实现应用服务、业务协调器和命令处理。
这一层负责业务流程的编排和协调，但不包含业务规则。
"""

from .strategies import *
from .strategy_engine import *
from .backtest_engine import *
from .optimizer import *
from .risk_manager import *
from .signal_processor import *
from .services import *
from .orchestrators import *

__all__ = [
    # Strategies
    "IStrategy",
    "SimpleMovingAverageCrossStrategy",
    "RSIStrategy",
    "StrategyContext",
    "StrategyMetrics",
    "STRATEGY_REGISTRY",

    # Strategy Engine
    "StrategyEngine",
    "SignalBus",
    "ISignalBus",
    "StrategyScheduler",
    "StrategyContextManager",

    # Backtest Engine
    "BacktestEngine",
    "BacktestConfig",
    "BacktestResult",
    "BacktestSimulator",

    # Optimizer
    "ParameterOptimizer",
    "OptimizationConfig",
    "OptimizationResult",
    "ParameterRange",
    "IOptimizationAlgorithm",

    # Risk Manager
    "RiskManager",
    "RiskLimits",
    "RiskMetrics",
    "IRiskRule",

    # Signal Processor
    "SignalProcessor",
    "SignalProcessingResult",
    "SignalAction",
    "ISignalFilter",
    "ISignalProcessor",

    # Services
    "DataService",
    "StrategyService",
    "BacktestService",
    "PortfolioService",
    "ServiceStatus",

    # Orchestrators
    "TradingOrchestrator",
    "BacktestOrchestrator",
    "TradingSession",
    "UpdatePortfolioCommand",
    "FetchDataCommand",
]
