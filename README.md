# 个人量化交易系统 (Personal Quantitative Trading System)

[![Python 3.13+](https://img.shields.io/badge/python-3.13+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

基于Python 3.13构建的现代化量化交易系统，采用分层架构设计，支持多策略并行执行、实时数据处理、回测分析等功能。

## ✨ 特性

### 🏗️ 架构特性
- **分层架构设计**：展示层、应用层、领域层、基础设施层清晰分离
- **深度模块化**：每个模块封装复杂逻辑，提供简洁接口
- **信息隐藏**：通过抽象接口隐藏实现细节
- **插件架构**：支持动态扩展和第三方插件

### 🚀 Python 3.13 新特性应用
- **Free-threaded CPython**：真正的并行处理能力
- **改进的错误消息**：更好的调试体验
- **新的交互式解释器**：开发调试更便捷
- **类型系统增强**：更强的类型安全保障
- **性能优化**：利用JIT编译器提升性能

### 📊 功能特性
- **实时数据处理**：支持多数据源实时数据获取和处理
- **策略引擎**：支持策略热加载和动态配置
- **高级回测**：完整的回测框架和性能分析
- **风险管理**：内置风险控制和资金管理
- **可视化界面**：现代化的GUI界面
- **机器学习集成**：预留ML模型接口

## 🏛️ 系统架构

```
┌─────────────────────────────────────────────────────┐
│                  展示层 (Presentation)               │
├─────────────────────────────────────────────────────┤
│                  应用层 (Application)                │
├─────────────────────────────────────────────────────┤
│                  领域层 (Domain)                     │
├─────────────────────────────────────────────────────┤
│                  基础设施层 (Infrastructure)          │
├─────────────────────────────────────────────────────┤
│                  共享组件 (Shared)                   │
└─────────────────────────────────────────────────────┘
```

### 模块组织

```
QuantTradingSystem/
├── presentation/          # 展示层
│   ├── gui/              # GUI组件
│   ├── controllers/      # 控制器
│   └── viewmodels/       # 视图模型
├── application/          # 应用层
│   ├── services/         # 应用服务
│   ├── orchestrators/    # 业务协调器
│   └── commands/         # 命令处理
├── domain/               # 领域层
│   ├── entities/         # 实体对象
│   ├── value_objects/    # 值对象
│   ├── repositories/     # 仓储接口
│   └── services/         # 领域服务
├── infrastructure/       # 基础设施层
│   ├── data_sources/     # 数据源实现
│   ├── repositories/     # 仓储实现
│   ├── external/         # 外部服务
│   └── config/           # 配置管理
└── shared/               # 共享组件
    ├── exceptions/       # 异常定义
    ├── utils/            # 工具函数
    └── constants/        # 常量定义
```

## 🚀 快速开始

### 环境要求

- Python 3.13+
- 推荐使用虚拟环境

### 安装

1. 克隆项目
```bash
git clone https://github.com/augment/quant-trading-system.git
cd quant-trading-system
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -e .
```

4. 安装开发依赖（可选）
```bash
pip install -e ".[dev]"
```

### 配置

1. 复制配置模板
```bash
cp config/system.yaml.template config/system.yaml
cp config/data_sources.yaml.template config/data_sources.yaml
cp config/strategies.yaml.template config/strategies.yaml
```

2. 编辑配置文件，填入API密钥等信息

3. 设置环境变量
```bash
export BINANCE_API_KEY="your_api_key"
export BINANCE_SECRET_KEY="your_secret_key"
```

### 运行

```bash
# 启动主程序
qts

# 或直接运行
python -m QuantTradingSystem.main
```

## 📖 使用指南

### 基本使用

```python
from QuantTradingSystem import TradingSystem

# 初始化系统
system = TradingSystem()

# 加载策略
system.load_strategy("sma_cross", config={
    "fast_period": 10,
    "slow_period": 30
})

# 启动实时交易
system.start_trading()
```

### 回测示例

```python
from QuantTradingSystem import BacktestEngine
from datetime import datetime, timedelta

# 创建回测引擎
backtest = BacktestEngine()

# 配置回测参数
config = {
    "symbol": "BTCUSDT",
    "start_date": datetime.now() - timedelta(days=365),
    "end_date": datetime.now(),
    "initial_capital": 10000,
    "strategy": "sma_cross"
}

# 运行回测
result = await backtest.run_backtest(config)
print(f"总收益率: {result.total_return:.2%}")
print(f"夏普比率: {result.sharpe_ratio:.2f}")
```

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest -m unit

# 运行集成测试
pytest -m integration

# 生成覆盖率报告
pytest --cov=QuantTradingSystem --cov-report=html
```

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢 Python 3.13 带来的新特性
- 感谢开源社区的贡献
- 感谢所有测试用户的反馈

## 📞 联系

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件至 <EMAIL>
- 加入我们的讨论群

---

**注意**: 本系统仅供学习和研究使用，实际交易请谨慎操作，风险自负。
