"""
业务协调器模块

业务协调器 - 协调多个服务，实现复杂的业务流程。
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
import threading

from domain.value_objects import MarketData, Signal
from domain.entities import Portfolio
from shared.constants import SignalType, TimeFrame
from shared.exceptions import SystemException
from shared.utils import Logger, AsyncUtils
from infrastructure.config import ConfigurationManager
from infrastructure.external import NotificationManager
from application.services import DataService, StrategyService, BacktestService, PortfolioService, ServiceStatus


@dataclass
class TradingSession:
    """交易会话"""
    session_id: str
    portfolio_id: str
    symbols: List[str]
    strategies: List[str]
    start_time: datetime
    end_time: Optional[datetime] = None
    is_active: bool = True
    paper_trading: bool = True
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class TradingOrchestrator:
    """
    交易协调器
    
    协调数据、策略、风险管理等服务，实现完整的交易流程。
    """
    
    def __init__(
        self, 
        config: ConfigurationManager,
        data_service: DataService,
        strategy_service: StrategyService,
        portfolio_service: PortfolioService,
        notification_manager: NotificationManager
    ):
        self.config = config
        self.data_service = data_service
        self.strategy_service = strategy_service
        self.portfolio_service = portfolio_service
        self.notification_manager = notification_manager
        self.logger = Logger.get_logger("trading_orchestrator")
        
        # 交易会话
        self._active_sessions: Dict[str, TradingSession] = {}
        self._data_subscriptions: Dict[str, str] = {}  # session_id -> subscription_id
        self._lock = threading.RLock()
        
        # 服务状态
        self.status = ServiceStatus()
    
    async def start(self) -> None:
        """启动交易协调器"""
        with self._lock:
            if self.status.is_running:
                return
            
            self.status.is_running = True
            self.status.start_time = datetime.now()
        
        # 启动依赖服务
        await self.data_service.start()
        await self.strategy_service.start()
        await self.portfolio_service.start()
        
        self.logger.info("交易协调器已启动")
    
    async def stop(self) -> None:
        """停止交易协调器"""
        with self._lock:
            if not self.status.is_running:
                return
            
            self.status.is_running = False
        
        # 停止所有交易会话
        for session_id in list(self._active_sessions.keys()):
            await self.stop_trading_session(session_id)
        
        # 停止依赖服务
        await self.strategy_service.stop()
        await self.portfolio_service.stop()
        await self.data_service.stop()
        
        self.logger.info("交易协调器已停止")
    
    async def start_trading_session(
        self,
        portfolio_id: str,
        symbols: List[str],
        strategies: List[str],
        paper_trading: bool = True,
        session_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        启动交易会话
        
        Args:
            portfolio_id: 投资组合ID
            symbols: 交易品种列表
            strategies: 策略列表
            paper_trading: 是否模拟交易
            session_config: 会话配置
            
        Returns:
            会话ID
        """
        try:
            # 验证投资组合
            portfolio = self.portfolio_service.get_portfolio(portfolio_id)
            if not portfolio:
                raise SystemException(f"投资组合不存在: {portfolio_id}")
            
            # 创建交易会话
            session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            session = TradingSession(
                session_id=session_id,
                portfolio_id=portfolio_id,
                symbols=symbols,
                strategies=strategies,
                start_time=datetime.now(),
                paper_trading=paper_trading,
                metadata=session_config or {}
            )
            
            # 加载策略
            for strategy_id in strategies:
                await self.strategy_service.load_strategy(strategy_id)
            
            # 订阅实时数据
            subscription_id = await self.data_service.subscribe_realtime_data(
                symbols,
                lambda data: asyncio.create_task(self._handle_market_data(session_id, data))
            )
            
            with self._lock:
                self._active_sessions[session_id] = session
                self._data_subscriptions[session_id] = subscription_id
            
            # 发送通知
            await self.notification_manager.send_notification(
                title="交易会话启动",
                content=f"会话ID: {session_id}\n品种: {', '.join(symbols)}\n策略: {', '.join(strategies)}",
                metadata={
                    "session_id": session_id,
                    "portfolio_id": portfolio_id,
                    "paper_trading": paper_trading
                }
            )
            
            self.logger.info(f"交易会话已启动: {session_id}")
            
            return session_id
            
        except Exception as e:
            self.logger.error(f"启动交易会话失败: {e}")
            raise SystemException(f"启动交易会话失败: {e}")
    
    async def stop_trading_session(self, session_id: str) -> bool:
        """停止交易会话"""
        try:
            with self._lock:
                session = self._active_sessions.get(session_id)
                if not session:
                    return False
                
                session.is_active = False
                session.end_time = datetime.now()
                
                # 取消数据订阅
                subscription_id = self._data_subscriptions.get(session_id)
                if subscription_id:
                    self.data_service.unsubscribe_realtime_data(subscription_id)
                    del self._data_subscriptions[session_id]
                
                del self._active_sessions[session_id]
            
            # 发送通知
            await self.notification_manager.send_notification(
                title="交易会话停止",
                content=f"会话ID: {session_id}",
                metadata={"session_id": session_id}
            )
            
            self.logger.info(f"交易会话已停止: {session_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"停止交易会话失败: {e}")
            return False
    
    async def _handle_market_data(self, session_id: str, data: MarketData) -> None:
        """处理市场数据"""
        try:
            session = self._active_sessions.get(session_id)
            if not session or not session.is_active:
                return
            
            # 执行策略
            await self.strategy_service.execute_strategies(data)
            
        except Exception as e:
            self.logger.error(f"处理市场数据失败: {e}")
    
    def get_active_sessions(self) -> Dict[str, TradingSession]:
        """获取活跃的交易会话"""
        with self._lock:
            return self._active_sessions.copy()
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        session = self._active_sessions.get(session_id)
        if not session:
            return None
        
        return {
            'session_id': session.session_id,
            'portfolio_id': session.portfolio_id,
            'symbols': session.symbols,
            'strategies': session.strategies,
            'start_time': session.start_time,
            'end_time': session.end_time,
            'is_active': session.is_active,
            'paper_trading': session.paper_trading,
            'duration': (datetime.now() - session.start_time).total_seconds() if session.is_active else None,
            'metadata': session.metadata
        }
    
    def get_status(self) -> ServiceStatus:
        """获取服务状态"""
        with self._lock:
            return self.status


class BacktestOrchestrator:
    """
    回测协调器
    
    协调回测相关的服务，实现完整的回测流程。
    """
    
    def __init__(
        self,
        config: ConfigurationManager,
        data_service: DataService,
        backtest_service: BacktestService,
        notification_manager: NotificationManager
    ):
        self.config = config
        self.data_service = data_service
        self.backtest_service = backtest_service
        self.notification_manager = notification_manager
        self.logger = Logger.get_logger("backtest_orchestrator")
        
        # 服务状态
        self.status = ServiceStatus()
        self._lock = threading.RLock()
    
    async def start(self) -> None:
        """启动回测协调器"""
        with self._lock:
            if self.status.is_running:
                return
            
            self.status.is_running = True
            self.status.start_time = datetime.now()
        
        # 启动依赖服务
        await self.data_service.start()
        await self.backtest_service.start()
        
        self.logger.info("回测协调器已启动")
    
    async def stop(self) -> None:
        """停止回测协调器"""
        with self._lock:
            if not self.status.is_running:
                return
            
            self.status.is_running = False
        
        # 停止依赖服务
        await self.backtest_service.stop()
        await self.data_service.stop()
        
        self.logger.info("回测协调器已停止")
    
    async def run_comprehensive_backtest(
        self,
        strategy_name: str,
        symbols: List[str],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        timeframes: Optional[List[TimeFrame]] = None,
        initial_capital: float = 100000.0,
        benchmark_symbol: Optional[str] = None,
        progress_callback: Optional[Callable[[str, float], None]] = None
    ) -> Dict[str, Any]:
        """
        运行综合回测
        
        Args:
            strategy_name: 策略名称
            symbols: 交易品种列表
            start_date: 开始日期
            end_date: 结束日期
            timeframes: 时间周期列表
            initial_capital: 初始资金
            benchmark_symbol: 基准品种
            progress_callback: 进度回调函数
            
        Returns:
            综合回测结果
        """
        try:
            # 设置默认值
            if start_date is None:
                start_date = datetime.now() - timedelta(days=365)
            if end_date is None:
                end_date = datetime.now()
            if timeframes is None:
                timeframes = [TimeFrame.D1]
            
            results = {}
            total_tests = len(symbols) * len(timeframes)
            completed_tests = 0
            
            # 发送开始通知
            await self.notification_manager.send_notification(
                title="综合回测开始",
                content=f"策略: {strategy_name}\n品种数量: {len(symbols)}\n时间周期: {len(timeframes)}",
                metadata={
                    "strategy_name": strategy_name,
                    "total_tests": total_tests
                }
            )
            
            # 对每个品种和时间周期运行回测
            for symbol in symbols:
                results[symbol] = {}
                
                for timeframe in timeframes:
                    try:
                        # 更新进度
                        if progress_callback:
                            progress = completed_tests / total_tests
                            progress_callback(f"回测 {symbol} - {timeframe.value}", progress)
                        
                        # 运行回测
                        result = await self.backtest_service.run_backtest(
                            strategy_name=strategy_name,
                            symbol=symbol,
                            start_date=start_date,
                            end_date=end_date,
                            initial_capital=initial_capital
                        )
                        
                        results[symbol][timeframe.value] = {
                            'performance': result.performance.to_dict(),
                            'trade_count': len(result.trade_history),
                            'signal_count': len(result.signal_history),
                            'status': result.status.value,
                            'error_message': result.error_message
                        }
                        
                        completed_tests += 1
                        
                    except Exception as e:
                        self.logger.error(f"回测失败 {symbol} - {timeframe.value}: {e}")
                        results[symbol][timeframe.value] = {
                            'error': str(e),
                            'status': 'FAILED'
                        }
                        completed_tests += 1
            
            # 计算汇总统计
            summary = self._calculate_backtest_summary(results)
            
            # 发送完成通知
            await self.notification_manager.send_notification(
                title="综合回测完成",
                content=f"策略: {strategy_name}\n完成测试: {completed_tests}/{total_tests}\n平均收益率: {summary.get('avg_return', 0):.2%}",
                metadata={
                    "strategy_name": strategy_name,
                    "completed_tests": completed_tests,
                    "total_tests": total_tests,
                    "summary": summary
                }
            )
            
            return {
                'strategy_name': strategy_name,
                'test_period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'results': results,
                'summary': summary,
                'completed_tests': completed_tests,
                'total_tests': total_tests
            }
            
        except Exception as e:
            self.logger.error(f"综合回测失败: {e}")
            
            # 发送错误通知
            await self.notification_manager.send_error_notification(
                f"综合回测失败: {e}",
                metadata={
                    "strategy_name": strategy_name,
                    "error": str(e)
                }
            )
            
            raise SystemException(f"综合回测失败: {e}")
    
    def _calculate_backtest_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """计算回测汇总统计"""
        all_returns = []
        all_sharpe_ratios = []
        successful_tests = 0
        failed_tests = 0
        
        for symbol_results in results.values():
            for timeframe_result in symbol_results.values():
                if 'performance' in timeframe_result:
                    performance = timeframe_result['performance']
                    all_returns.append(performance.get('total_return', 0))
                    all_sharpe_ratios.append(performance.get('sharpe_ratio', 0))
                    successful_tests += 1
                else:
                    failed_tests += 1
        
        if all_returns:
            import statistics
            summary = {
                'avg_return': statistics.mean(all_returns),
                'median_return': statistics.median(all_returns),
                'std_return': statistics.stdev(all_returns) if len(all_returns) > 1 else 0,
                'min_return': min(all_returns),
                'max_return': max(all_returns),
                'avg_sharpe_ratio': statistics.mean(all_sharpe_ratios),
                'successful_tests': successful_tests,
                'failed_tests': failed_tests,
                'success_rate': successful_tests / (successful_tests + failed_tests) if (successful_tests + failed_tests) > 0 else 0
            }
        else:
            summary = {
                'avg_return': 0,
                'median_return': 0,
                'std_return': 0,
                'min_return': 0,
                'max_return': 0,
                'avg_sharpe_ratio': 0,
                'successful_tests': successful_tests,
                'failed_tests': failed_tests,
                'success_rate': 0
            }
        
        return summary
    
    def get_status(self) -> ServiceStatus:
        """获取服务状态"""
        with self._lock:
            return self.status
