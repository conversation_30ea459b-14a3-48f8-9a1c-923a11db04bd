"""
值对象模块

定义系统中的值对象，这些对象是不可变的，用于表示业务概念。
利用Python 3.13的dataclass和类型注解增强功能。
"""

from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from shared.constants import (
    SignalType, OrderType, OrderStatus, OrderSide, 
    AssetType, PositionSide, TimeFrame
)
from shared.exceptions import ValidationException


@dataclass(frozen=True)
class Price:
    """价格值对象"""
    value: Decimal
    currency: str = "USD"
    
    def __post_init__(self):
        if self.value <= 0:
            raise ValidationException("价格必须大于0", field_name="value", field_value=self.value)
        if not self.currency or len(self.currency) != 3:
            raise ValidationException("货币代码必须是3位字符", field_name="currency", field_value=self.currency)
    
    def __str__(self) -> str:
        return f"{self.value:.4f} {self.currency}"
    
    def __add__(self, other: 'Price') -> 'Price':
        if self.currency != other.currency:
            raise ValidationException("不能相加不同货币的价格")
        return Price(self.value + other.value, self.currency)
    
    def __sub__(self, other: 'Price') -> 'Price':
        if self.currency != other.currency:
            raise ValidationException("不能相减不同货币的价格")
        return Price(self.value - other.value, self.currency)
    
    def __mul__(self, multiplier: Union[int, float, Decimal]) -> 'Price':
        return Price(self.value * Decimal(str(multiplier)), self.currency)
    
    def __truediv__(self, divisor: Union[int, float, Decimal]) -> 'Price':
        if divisor == 0:
            raise ValidationException("除数不能为0")
        return Price(self.value / Decimal(str(divisor)), self.currency)


@dataclass(frozen=True)
class Volume:
    """成交量值对象"""
    value: Decimal
    
    def __post_init__(self):
        if self.value < 0:
            raise ValidationException("成交量不能为负数", field_name="value", field_value=self.value)
    
    def __str__(self) -> str:
        return f"{self.value:.8f}"
    
    def __add__(self, other: 'Volume') -> 'Volume':
        return Volume(self.value + other.value)
    
    def __sub__(self, other: 'Volume') -> 'Volume':
        result = self.value - other.value
        if result < 0:
            raise ValidationException("成交量相减结果不能为负数")
        return Volume(result)


@dataclass(frozen=True)
class MarketData:
    """市场数据值对象"""
    symbol: str
    timestamp: datetime
    open: Price
    high: Price
    low: Price
    close: Price
    volume: Volume
    timeframe: TimeFrame = TimeFrame.D1
    asset_type: AssetType = AssetType.STOCK
    
    def __post_init__(self):
        # 验证价格关系
        if self.high.value < self.low.value:
            raise ValidationException("最高价不能低于最低价")
        if self.high.value < max(self.open.value, self.close.value):
            raise ValidationException("最高价不能低于开盘价或收盘价")
        if self.low.value > min(self.open.value, self.close.value):
            raise ValidationException("最低价不能高于开盘价或收盘价")
        
        # 验证货币一致性
        currencies = {self.open.currency, self.high.currency, self.low.currency, self.close.currency}
        if len(currencies) > 1:
            raise ValidationException("所有价格必须使用相同货币")
    
    @property
    def typical_price(self) -> Price:
        """典型价格 (HLC/3)"""
        return Price(
            (self.high.value + self.low.value + self.close.value) / 3,
            self.close.currency
        )
    
    @property
    def price_change(self) -> Price:
        """价格变化"""
        return self.close - self.open
    
    @property
    def price_change_percent(self) -> float:
        """价格变化百分比"""
        if self.open.value == 0:
            return 0.0
        return float((self.close.value - self.open.value) / self.open.value)
    
    @property
    def is_bullish(self) -> bool:
        """是否看涨（收盘价高于开盘价）"""
        return self.close.value > self.open.value
    
    @property
    def is_bearish(self) -> bool:
        """是否看跌（收盘价低于开盘价）"""
        return self.close.value < self.open.value


@dataclass(frozen=True)
class Signal:
    """交易信号值对象"""
    id: UUID = field(default_factory=uuid4)
    strategy_id: str = ""
    symbol: str = ""
    signal_type: SignalType = SignalType.HOLD
    strength: float = 0.0
    timestamp: datetime = field(default_factory=datetime.utcnow)
    price: Optional[Price] = None
    target_price: Optional[Price] = None
    stop_loss: Optional[Price] = None
    take_profit: Optional[Price] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not (0 <= self.strength <= 1):
            raise ValidationException(
                "信号强度必须在0-1之间", 
                field_name="strength", 
                field_value=self.strength
            )
        
        if not self.symbol:
            raise ValidationException("交易品种不能为空", field_name="symbol")
        
        if not self.strategy_id:
            raise ValidationException("策略ID不能为空", field_name="strategy_id")
    
    def is_valid(self) -> bool:
        """检查信号是否有效"""
        try:
            # 基本验证
            if self.timestamp is None:
                return False
            if not (0 <= self.strength <= 1):
                return False
            if self.signal_type not in SignalType:
                return False
            
            # 价格验证
            if self.price and self.price.value <= 0:
                return False
            if self.target_price and self.target_price.value <= 0:
                return False
            if self.stop_loss and self.stop_loss.value <= 0:
                return False
            if self.take_profit and self.take_profit.value <= 0:
                return False
            
            return True
        except Exception:
            return False
    
    def is_actionable(self) -> bool:
        """检查信号是否可执行"""
        return (
            self.is_valid() and 
            self.signal_type in [SignalType.BUY, SignalType.SELL, SignalType.STRONG_BUY, SignalType.STRONG_SELL] and
            self.strength >= 0.5
        )


@dataclass(frozen=True)
class Order:
    """订单值对象"""
    id: UUID = field(default_factory=uuid4)
    symbol: str = ""
    order_type: OrderType = OrderType.MARKET
    side: OrderSide = OrderSide.BUY
    quantity: Volume = field(default_factory=lambda: Volume(Decimal('0')))
    price: Optional[Price] = None
    stop_price: Optional[Price] = None
    status: OrderStatus = OrderStatus.CREATED
    timestamp: datetime = field(default_factory=datetime.utcnow)
    filled_quantity: Volume = field(default_factory=lambda: Volume(Decimal('0')))
    average_fill_price: Optional[Price] = None
    commission: Optional[Price] = None
    strategy_id: Optional[str] = None
    signal_id: Optional[UUID] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.symbol:
            raise ValidationException("交易品种不能为空", field_name="symbol")
        
        if self.quantity.value <= 0:
            raise ValidationException("订单数量必须大于0", field_name="quantity")
        
        if self.filled_quantity.value > self.quantity.value:
            raise ValidationException("已成交数量不能大于订单数量")
        
        # 限价单必须有价格
        if self.order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT] and not self.price:
            raise ValidationException("限价单必须指定价格")
        
        # 止损单必须有止损价格
        if self.order_type in [OrderType.STOP, OrderType.STOP_LIMIT] and not self.stop_price:
            raise ValidationException("止损单必须指定止损价格")
    
    @property
    def is_filled(self) -> bool:
        """是否完全成交"""
        return self.status == OrderStatus.FILLED
    
    @property
    def is_partial_filled(self) -> bool:
        """是否部分成交"""
        return self.status == OrderStatus.PARTIAL_FILLED
    
    @property
    def is_active(self) -> bool:
        """是否为活跃订单"""
        return self.status in [OrderStatus.SUBMITTED, OrderStatus.PARTIAL_FILLED]
    
    @property
    def remaining_quantity(self) -> Volume:
        """剩余数量"""
        return Volume(self.quantity.value - self.filled_quantity.value)
    
    @property
    def fill_percentage(self) -> float:
        """成交百分比"""
        if self.quantity.value == 0:
            return 0.0
        return float(self.filled_quantity.value / self.quantity.value)
    
    @property
    def total_value(self) -> Optional[Price]:
        """订单总价值"""
        if not self.average_fill_price:
            return None
        return self.average_fill_price * float(self.filled_quantity.value)


@dataclass(frozen=True)
class PerformanceMetrics:
    """性能指标值对象"""
    total_return: float = 0.0
    annualized_return: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    max_drawdown: float = 0.0
    calmar_ratio: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    alpha: float = 0.0
    beta: float = 0.0
    information_ratio: float = 0.0
    var_95: float = 0.0  # 95% VaR
    cvar_95: float = 0.0  # 95% CVaR
    
    def __post_init__(self):
        # 验证百分比字段
        percentage_fields = ['win_rate', 'max_drawdown']
        for field_name in percentage_fields:
            value = getattr(self, field_name)
            if not (0 <= value <= 1):
                raise ValidationException(
                    f"{field_name}必须在0-1之间",
                    field_name=field_name,
                    field_value=value
                )
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典"""
        return {
            'total_return': self.total_return,
            'annualized_return': self.annualized_return,
            'volatility': self.volatility,
            'sharpe_ratio': self.sharpe_ratio,
            'sortino_ratio': self.sortino_ratio,
            'max_drawdown': self.max_drawdown,
            'calmar_ratio': self.calmar_ratio,
            'win_rate': self.win_rate,
            'profit_factor': self.profit_factor,
            'alpha': self.alpha,
            'beta': self.beta,
            'information_ratio': self.information_ratio,
            'var_95': self.var_95,
            'cvar_95': self.cvar_95,
        }
    
    def is_profitable(self) -> bool:
        """是否盈利"""
        return self.total_return > 0
    
    def is_outperforming(self, benchmark_return: float = 0.0) -> bool:
        """是否跑赢基准"""
        return self.total_return > benchmark_return
