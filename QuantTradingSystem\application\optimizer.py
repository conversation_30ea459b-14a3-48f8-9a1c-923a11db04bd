"""
参数优化器模块

参数优化核心模块 - 封装复杂的参数优化逻辑，支持多种优化算法。
"""

import asyncio
import itertools
import random
import math
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Callable, Type, Awaitable
from dataclasses import dataclass, field
from uuid import uuid4
import threading

from domain.value_objects import PerformanceMetrics
from shared.constants import OptimizationMethod
from shared.exceptions import BacktestException
from shared.utils import Logger, PerformanceUtils
from infrastructure.config import ConfigurationManager
from application.strategies import IStrategy, STRATEGY_REGISTRY
from application.backtest_engine import BacktestEngine, BacktestConfig, BacktestResult


@dataclass
class ParameterRange:
    """参数范围"""
    name: str
    min_value: float = 0.0
    max_value: float = 0.0
    step: float = 0.0
    values: List[Any] = None
    
    def __post_init__(self):
        if self.values is None:
            self.values = []
    
    def get_values(self) -> List[Any]:
        """获取参数值列表"""
        if self.values:
            return self.values
        
        if self.step <= 0:
            return []
        
        values = []
        current = self.min_value
        while current <= self.max_value:
            values.append(current)
            current += self.step
        
        return values


@dataclass
class OptimizationConfig:
    """优化配置"""
    strategy_id: str
    parameters: List[ParameterRange]
    backtest_config: BacktestConfig
    method: OptimizationMethod = OptimizationMethod.GRID_SEARCH
    objective: str = "sharpe_ratio"
    max_iterations: int = 100
    random_samples: int = 30
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class OptimizationResult:
    """优化结果"""
    optimization_id: str
    config: OptimizationConfig
    best_parameters: Dict[str, Any]
    best_performance: PerformanceMetrics
    all_results: List[Tuple[Dict[str, Any], PerformanceMetrics]]
    start_time: datetime
    end_time: Optional[datetime] = None
    total_iterations: int = 0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class IOptimizationAlgorithm(ABC):
    """优化算法接口"""
    
    @abstractmethod
    async def optimize(
        self, 
        config: OptimizationConfig,
        evaluate_func: Callable[[Dict[str, Any]], Awaitable[PerformanceMetrics]],
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> OptimizationResult:
        """执行优化"""
        pass


class GridSearchOptimizer(IOptimizationAlgorithm):
    """网格搜索优化器"""
    
    def __init__(self):
        self.logger = Logger.get_logger("optimizer.grid_search")
    
    async def optimize(
        self, 
        config: OptimizationConfig,
        evaluate_func: Callable[[Dict[str, Any]], Awaitable[PerformanceMetrics]],
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> OptimizationResult:
        """执行网格搜索优化"""
        try:
            # 生成参数网格
            param_values = []
            param_names = []
            
            for param in config.parameters:
                values = param.get_values()
                if not values:
                    self.logger.warning(f"参数{param.name}没有有效值")
                    continue
                
                param_values.append(values)
                param_names.append(param.name)
            
            if not param_values:
                raise ValueError("没有有效的参数范围")
            
            # 生成所有参数组合
            param_combinations = list(itertools.product(*param_values))
            total_combinations = len(param_combinations)
            
            self.logger.info(f"网格搜索: {total_combinations}个参数组合")
            
            # 初始化结果
            optimization_id = str(uuid4())
            result = OptimizationResult(
                optimization_id=optimization_id,
                config=config,
                best_parameters={},
                best_performance=PerformanceMetrics(),
                all_results=[],
                start_time=datetime.now(),
                total_iterations=total_combinations
            )
            
            # 评估所有组合
            best_score = float('-inf')
            best_params = {}
            all_results = []
            
            for i, combination in enumerate(param_combinations):
                # 构建参数字典
                params = {param_names[j]: combination[j] for j in range(len(param_names))}
                
                # 评估参数
                performance = await evaluate_func(params)
                
                # 获取目标指标
                score = self._get_objective_score(performance, config.objective)
                
                # 记录结果
                all_results.append((params, performance))
                
                # 更新最佳结果
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                    result.best_parameters = best_params
                    result.best_performance = performance
                
                # 更新进度
                if progress_callback:
                    progress = (i + 1) / total_combinations
                    progress_callback(progress)
            
            # 完成优化
            result.all_results = all_results
            result.end_time = datetime.now()
            
            self.logger.info(f"网格搜索完成: 最佳参数 {best_params}, 得分 {best_score}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"网格搜索失败: {e}")
            
            result = OptimizationResult(
                optimization_id=str(uuid4()),
                config=config,
                best_parameters={},
                best_performance=PerformanceMetrics(),
                all_results=[],
                start_time=datetime.now(),
                end_time=datetime.now(),
                error_message=str(e)
            )
            
            return result
    
    def _get_objective_score(self, performance: PerformanceMetrics, objective: str) -> float:
        """获取目标指标得分"""
        if objective == "sharpe_ratio":
            return performance.sharpe_ratio
        elif objective == "total_return":
            return performance.total_return
        elif objective == "calmar_ratio":
            return performance.calmar_ratio
        elif objective == "sortino_ratio":
            return performance.sortino_ratio
        else:
            return performance.sharpe_ratio  # 默认使用夏普比率


class RandomSearchOptimizer(IOptimizationAlgorithm):
    """随机搜索优化器"""
    
    def __init__(self):
        self.logger = Logger.get_logger("optimizer.random_search")
    
    async def optimize(
        self, 
        config: OptimizationConfig,
        evaluate_func: Callable[[Dict[str, Any]], Awaitable[PerformanceMetrics]],
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> OptimizationResult:
        """执行随机搜索优化"""
        try:
            # 初始化结果
            optimization_id = str(uuid4())
            result = OptimizationResult(
                optimization_id=optimization_id,
                config=config,
                best_parameters={},
                best_performance=PerformanceMetrics(),
                all_results=[],
                start_time=datetime.now(),
                total_iterations=config.random_samples
            )
            
            # 评估随机样本
            best_score = float('-inf')
            best_params = {}
            all_results = []
            
            for i in range(config.random_samples):
                # 生成随机参数
                params = self._generate_random_params(config.parameters)
                
                # 评估参数
                performance = await evaluate_func(params)
                
                # 获取目标指标
                score = self._get_objective_score(performance, config.objective)
                
                # 记录结果
                all_results.append((params, performance))
                
                # 更新最佳结果
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                    result.best_parameters = best_params
                    result.best_performance = performance
                
                # 更新进度
                if progress_callback:
                    progress = (i + 1) / config.random_samples
                    progress_callback(progress)
            
            # 完成优化
            result.all_results = all_results
            result.end_time = datetime.now()
            
            self.logger.info(f"随机搜索完成: 最佳参数 {best_params}, 得分 {best_score}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"随机搜索失败: {e}")
            
            result = OptimizationResult(
                optimization_id=str(uuid4()),
                config=config,
                best_parameters={},
                best_performance=PerformanceMetrics(),
                all_results=[],
                start_time=datetime.now(),
                end_time=datetime.now(),
                error_message=str(e)
            )
            
            return result
    
    def _generate_random_params(self, parameters: List[ParameterRange]) -> Dict[str, Any]:
        """生成随机参数"""
        params = {}
        
        for param in parameters:
            if param.values:
                # 从离散值中随机选择
                params[param.name] = random.choice(param.values)
            else:
                # 在范围内随机生成
                if isinstance(param.min_value, int) and isinstance(param.max_value, int):
                    # 整数参数
                    params[param.name] = random.randint(param.min_value, param.max_value)
                else:
                    # 浮点数参数
                    params[param.name] = random.uniform(param.min_value, param.max_value)
        
        return params
    
    def _get_objective_score(self, performance: PerformanceMetrics, objective: str) -> float:
        """获取目标指标得分"""
        if objective == "sharpe_ratio":
            return performance.sharpe_ratio
        elif objective == "total_return":
            return performance.total_return
        elif objective == "calmar_ratio":
            return performance.calmar_ratio
        elif objective == "sortino_ratio":
            return performance.sortino_ratio
        else:
            return performance.sharpe_ratio  # 默认使用夏普比率


class BayesianOptimizer(IOptimizationAlgorithm):
    """贝叶斯优化器"""
    
    def __init__(self):
        self.logger = Logger.get_logger("optimizer.bayesian")
    
    async def optimize(
        self, 
        config: OptimizationConfig,
        evaluate_func: Callable[[Dict[str, Any]], Awaitable[PerformanceMetrics]],
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> OptimizationResult:
        """执行贝叶斯优化"""
        try:
            # 检查是否安装了scikit-optimize
            try:
                import skopt
                from skopt import gp_minimize
                from skopt.space import Real, Integer, Categorical
            except ImportError:
                self.logger.error("贝叶斯优化需要安装scikit-optimize库")
                raise ImportError("贝叶斯优化需要安装scikit-optimize库")
            
            # 初始化结果
            optimization_id = str(uuid4())
            result = OptimizationResult(
                optimization_id=optimization_id,
                config=config,
                best_parameters={},
                best_performance=PerformanceMetrics(),
                all_results=[],
                start_time=datetime.now(),
                total_iterations=config.max_iterations
            )
            
            # 构建参数空间
            param_space = []
            param_names = []
            
            for param in config.parameters:
                param_names.append(param.name)
                
                if param.values:
                    # 离散值
                    param_space.append(Categorical(param.values, name=param.name))
                elif isinstance(param.min_value, int) and isinstance(param.max_value, int):
                    # 整数参数
                    param_space.append(Integer(param.min_value, param.max_value, name=param.name))
                else:
                    # 浮点数参数
                    param_space.append(Real(param.min_value, param.max_value, name=param.name))
            
            # 存储所有评估结果
            all_results = []
            
            # 定义目标函数
            async def objective(params):
                # 构建参数字典
                param_dict = {param_names[i]: params[i] for i in range(len(params))}
                
                # 评估参数
                performance = await evaluate_func(param_dict)
                
                # 获取目标指标
                score = self._get_objective_score(performance, config.objective)
                
                # 记录结果
                all_results.append((param_dict, performance))
                
                # 更新最佳结果
                if len(all_results) == 1 or score > best_score[0]:
                    best_score[0] = score
                    best_params[0] = param_dict.copy()
                    result.best_parameters = param_dict
                    result.best_performance = performance
                
                # 更新进度
                if progress_callback:
                    progress = len(all_results) / config.max_iterations
                    progress_callback(progress)
                
                # 贝叶斯优化是最小化问题，所以返回负值
                return -score
            
            # 包装异步目标函数
            def sync_objective(params):
                loop = asyncio.get_event_loop()
                return loop.run_until_complete(objective(params))
            
            # 初始化最佳结果
            best_score = [float('-inf')]
            best_params = [{}]
            
            # 执行贝叶斯优化
            res = gp_minimize(
                sync_objective,
                param_space,
                n_calls=config.max_iterations,
                random_state=42
            )
            
            # 完成优化
            result.all_results = all_results
            result.end_time = datetime.now()
            result.total_iterations = len(all_results)
            
            self.logger.info(f"贝叶斯优化完成: 最佳参数 {best_params[0]}, 得分 {best_score[0]}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"贝叶斯优化失败: {e}")
            
            result = OptimizationResult(
                optimization_id=str(uuid4()),
                config=config,
                best_parameters={},
                best_performance=PerformanceMetrics(),
                all_results=[],
                start_time=datetime.now(),
                end_time=datetime.now(),
                error_message=str(e)
            )
            
            return result
    
    def _get_objective_score(self, performance: PerformanceMetrics, objective: str) -> float:
        """获取目标指标得分"""
        if objective == "sharpe_ratio":
            return performance.sharpe_ratio
        elif objective == "total_return":
            return performance.total_return
        elif objective == "calmar_ratio":
            return performance.calmar_ratio
        elif objective == "sortino_ratio":
            return performance.sortino_ratio
        else:
            return performance.sharpe_ratio  # 默认使用夏普比率


class ParameterOptimizer:
    """
    参数优化器
    
    参数优化核心模块 - 封装复杂的参数优化逻辑，支持多种优化算法。
    """
    
    def __init__(self, config: ConfigurationManager, backtest_engine: BacktestEngine):
        self.config = config
        self.backtest_engine = backtest_engine
        self.logger = Logger.get_logger("parameter_optimizer")
        
        # 初始化优化算法
        self.algorithms: Dict[OptimizationMethod, IOptimizationAlgorithm] = {
            OptimizationMethod.GRID_SEARCH: GridSearchOptimizer(),
            OptimizationMethod.RANDOM_SEARCH: RandomSearchOptimizer(),
            OptimizationMethod.BAYESIAN: BayesianOptimizer()
        }
        
        # 优化状态
        self._running_optimizations: Dict[str, OptimizationResult] = {}
        self._lock = threading.RLock()
    
    async def optimize_parameters(
        self, 
        optimization_config: OptimizationConfig,
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> OptimizationResult:
        """
        优化参数
        
        Args:
            optimization_config: 优化配置
            progress_callback: 进度回调函数
            
        Returns:
            优化结果
        """
        optimization_id = str(uuid4())
        
        try:
            # 获取优化算法
            algorithm = self.algorithms.get(optimization_config.method)
            if not algorithm:
                raise ValueError(f"不支持的优化方法: {optimization_config.method}")
            
            # 获取策略类
            strategy_class = STRATEGY_REGISTRY.get(optimization_config.strategy_id)
            if not strategy_class:
                raise ValueError(f"未知的策略: {optimization_config.strategy_id}")
            
            # 创建评估函数
            async def evaluate_parameters(params: Dict[str, Any]) -> PerformanceMetrics:
                # 创建策略实例
                strategy_config = self.config.get_config(f"strategies.default.{optimization_config.strategy_id}", {})
                strategy_config.update(params)
                
                strategy = strategy_class(optimization_config.strategy_id, strategy_config)
                
                # 运行回测
                backtest_result = await self.backtest_engine.run_backtest(
                    strategy,
                    optimization_config.backtest_config
                )
                
                return backtest_result.performance
            
            # 初始化结果
            result = OptimizationResult(
                optimization_id=optimization_id,
                config=optimization_config,
                best_parameters={},
                best_performance=PerformanceMetrics(),
                all_results=[],
                start_time=datetime.now()
            )
            
            with self._lock:
                self._running_optimizations[optimization_id] = result
            
            self.logger.info(f"开始参数优化: {optimization_id}, 策略: {optimization_config.strategy_id}")
            
            # 执行优化
            result = await algorithm.optimize(
                optimization_config,
                evaluate_parameters,
                progress_callback
            )
            
            self.logger.info(f"参数优化完成: {optimization_id}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"参数优化失败: {e}")
            
            result = OptimizationResult(
                optimization_id=optimization_id,
                config=optimization_config,
                best_parameters={},
                best_performance=PerformanceMetrics(),
                all_results=[],
                start_time=datetime.now(),
                end_time=datetime.now(),
                error_message=str(e)
            )
            
            return result
            
        finally:
            with self._lock:
                if optimization_id in self._running_optimizations:
                    del self._running_optimizations[optimization_id]
    
    def get_running_optimizations(self) -> Dict[str, OptimizationResult]:
        """获取正在运行的优化"""
        with self._lock:
            return self._running_optimizations.copy()
    
    def cancel_optimization(self, optimization_id: str) -> bool:
        """取消优化"""
        with self._lock:
            if optimization_id in self._running_optimizations:
                result = self._running_optimizations[optimization_id]
                result.end_time = datetime.now()
                result.error_message = "用户取消"
                del self._running_optimizations[optimization_id]
                self.logger.info(f"优化已取消: {optimization_id}")
                return True
            return False
