"""
策略引擎模块

策略执行引擎 - 封装复杂的策略管理逻辑，支持策略热加载和动态配置。
"""

import asyncio
import importlib
import sys
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Type
from dataclasses import dataclass
import threading

from domain.value_objects import MarketData, Signal
from domain.entities import Portfolio
from shared.constants import StrategyStatus
from shared.exceptions import StrategyException, StrategyLoadException, StrategyExecutionException
from shared.utils import Logger, PerformanceUtils, AsyncUtils
from infrastructure.config import ConfigurationManager
from application.strategies import IStrategy, StrategyContext, StrategyMetrics, STRATEGY_REGISTRY


@dataclass
class StrategyInfo:
    """策略信息"""
    strategy_id: str
    strategy_class: Type[IStrategy]
    config: Dict[str, Any]
    instance: Optional[IStrategy] = None
    last_update: Optional[datetime] = None
    enabled: bool = True


class ISignalBus(ABC):
    """信号总线接口"""
    
    @abstractmethod
    async def publish_signal(self, signal: Signal) -> None:
        """发布信号"""
        pass
    
    @abstractmethod
    def subscribe_signals(self, handler: Callable[[Signal], None]) -> str:
        """订阅信号"""
        pass
    
    @abstractmethod
    def unsubscribe_signals(self, subscription_id: str) -> bool:
        """取消订阅"""
        pass


class SignalBus(ISignalBus):
    """
    信号总线 - 实现发布-订阅模式
    
    解耦信号生成、处理和分发，支持复杂的信号路由。
    """
    
    def __init__(self):
        self.logger = Logger.get_logger("signal_bus")
        self._subscribers: Dict[str, Callable[[Signal], None]] = {}
        self._signal_queue = asyncio.Queue()
        self._processor_task: Optional[asyncio.Task] = None
        self._running = False
        self._lock = threading.RLock()
    
    async def start(self) -> None:
        """启动信号总线"""
        if self._running:
            return
        
        self._running = True
        self._processor_task = asyncio.create_task(self._process_signals())
        self.logger.info("信号总线已启动")
    
    async def stop(self) -> None:
        """停止信号总线"""
        if not self._running:
            return
        
        self._running = False
        
        if self._processor_task:
            self._processor_task.cancel()
            try:
                await self._processor_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("信号总线已停止")
    
    async def publish_signal(self, signal: Signal) -> None:
        """发布信号"""
        if not self._running:
            self.logger.warning("信号总线未启动，信号被丢弃")
            return
        
        await self._signal_queue.put(signal)
        self.logger.debug(f"信号已发布: {signal.strategy_id} - {signal.signal_type.value}")
    
    def subscribe_signals(self, handler: Callable[[Signal], None]) -> str:
        """订阅信号"""
        with self._lock:
            subscription_id = f"sub_{id(handler)}_{datetime.now().timestamp()}"
            self._subscribers[subscription_id] = handler
            self.logger.debug(f"信号订阅已添加: {subscription_id}")
            return subscription_id
    
    def unsubscribe_signals(self, subscription_id: str) -> bool:
        """取消订阅"""
        with self._lock:
            if subscription_id in self._subscribers:
                del self._subscribers[subscription_id]
                self.logger.debug(f"信号订阅已取消: {subscription_id}")
                return True
            return False
    
    async def _process_signals(self) -> None:
        """处理信号队列"""
        while self._running:
            try:
                # 等待信号，设置超时避免阻塞
                signal = await asyncio.wait_for(self._signal_queue.get(), timeout=1.0)
                
                # 分发信号给所有订阅者
                with self._lock:
                    subscribers = list(self._subscribers.values())
                
                for handler in subscribers:
                    try:
                        if asyncio.iscoroutinefunction(handler):
                            await handler(signal)
                        else:
                            handler(signal)
                    except Exception as e:
                        self.logger.error(f"信号处理器执行失败: {e}")
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"信号处理失败: {e}")


class StrategyScheduler:
    """策略调度器"""
    
    def __init__(self):
        self.logger = Logger.get_logger("strategy_scheduler")
        self._scheduled_strategies: Dict[str, Dict[str, Any]] = {}
        self._scheduler_task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start(self) -> None:
        """启动调度器"""
        if self._running:
            return
        
        self._running = True
        self._scheduler_task = asyncio.create_task(self._run_scheduler())
        self.logger.info("策略调度器已启动")
    
    async def stop(self) -> None:
        """停止调度器"""
        if not self._running:
            return
        
        self._running = False
        
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("策略调度器已停止")
    
    def schedule_strategy(
        self, 
        strategy_id: str, 
        interval: float,
        callback: Callable[[], None]
    ) -> None:
        """调度策略"""
        self._scheduled_strategies[strategy_id] = {
            'interval': interval,
            'callback': callback,
            'last_run': 0,
            'enabled': True
        }
        self.logger.info(f"策略已调度: {strategy_id}, 间隔: {interval}秒")
    
    def unschedule_strategy(self, strategy_id: str) -> bool:
        """取消调度"""
        if strategy_id in self._scheduled_strategies:
            del self._scheduled_strategies[strategy_id]
            self.logger.info(f"策略调度已取消: {strategy_id}")
            return True
        return False
    
    async def _run_scheduler(self) -> None:
        """运行调度器"""
        while self._running:
            try:
                current_time = datetime.now().timestamp()
                
                for strategy_id, schedule_info in self._scheduled_strategies.items():
                    if not schedule_info['enabled']:
                        continue
                    
                    last_run = schedule_info['last_run']
                    interval = schedule_info['interval']
                    
                    if current_time - last_run >= interval:
                        try:
                            callback = schedule_info['callback']
                            if asyncio.iscoroutinefunction(callback):
                                await callback()
                            else:
                                callback()
                            
                            schedule_info['last_run'] = current_time
                            
                        except Exception as e:
                            self.logger.error(f"调度策略执行失败 {strategy_id}: {e}")
                
                await asyncio.sleep(0.1)  # 避免CPU占用过高
                
            except Exception as e:
                self.logger.error(f"调度器运行错误: {e}")
                await asyncio.sleep(1)


class StrategyContextManager:
    """策略上下文管理器"""
    
    def __init__(self):
        self.logger = Logger.get_logger("strategy_context")
        self._contexts: Dict[str, StrategyContext] = {}
        self._lock = threading.RLock()
    
    def create_context(
        self, 
        strategy_id: str, 
        config: Dict[str, Any],
        portfolio: Optional[Portfolio] = None
    ) -> StrategyContext:
        """创建策略上下文"""
        with self._lock:
            context = StrategyContext(
                strategy_id=strategy_id,
                config=config,
                portfolio=portfolio
            )
            self._contexts[strategy_id] = context
            self.logger.debug(f"策略上下文已创建: {strategy_id}")
            return context
    
    def get_context(self, strategy_id: str) -> Optional[StrategyContext]:
        """获取策略上下文"""
        with self._lock:
            return self._contexts.get(strategy_id)
    
    def update_context_data(self, strategy_id: str, data: MarketData) -> None:
        """更新上下文数据"""
        with self._lock:
            context = self._contexts.get(strategy_id)
            if context:
                context.current_data = data
                context.historical_data.append(data)
                
                # 保持历史数据长度
                max_history = 1000
                if len(context.historical_data) > max_history:
                    context.historical_data = context.historical_data[-max_history:]
    
    def remove_context(self, strategy_id: str) -> bool:
        """移除策略上下文"""
        with self._lock:
            if strategy_id in self._contexts:
                del self._contexts[strategy_id]
                self.logger.debug(f"策略上下文已移除: {strategy_id}")
                return True
            return False


class StrategyEngine:
    """
    策略引擎
    
    策略执行引擎 - 封装复杂的策略管理逻辑，支持策略热加载和动态配置。
    """
    
    def __init__(self, config: ConfigurationManager, signal_bus: ISignalBus):
        self.config = config
        self.signal_bus = signal_bus
        self.logger = Logger.get_logger("strategy_engine")
        
        # 组件初始化
        self._strategies: Dict[str, StrategyInfo] = {}
        self._scheduler = StrategyScheduler()
        self._context_manager = StrategyContextManager()
        
        # 状态管理
        self._running = False
        self._lock = threading.RLock()
        
        # 性能监控
        self._execution_times: Dict[str, List[float]] = {}
    
    async def start(self) -> None:
        """启动策略引擎"""
        if self._running:
            return
        
        self._running = True
        
        # 启动组件
        await self.signal_bus.start()
        await self._scheduler.start()
        
        # 加载配置的策略
        await self._load_configured_strategies()
        
        self.logger.info("策略引擎已启动")
    
    async def stop(self) -> None:
        """停止策略引擎"""
        if not self._running:
            return
        
        self._running = False
        
        # 停止所有策略
        for strategy_info in self._strategies.values():
            if strategy_info.instance:
                await strategy_info.instance.stop()
        
        # 停止组件
        await self._scheduler.stop()
        await self.signal_bus.stop()
        
        self.logger.info("策略引擎已停止")
    
    async def register_strategy(
        self, 
        strategy_id: str, 
        strategy_class: Type[IStrategy],
        config: Dict[str, Any]
    ) -> None:
        """
        注册策略 - 支持热加载
        
        Args:
            strategy_id: 策略ID
            strategy_class: 策略类
            config: 策略配置
        """
        try:
            with self._lock:
                # 创建策略信息
                strategy_info = StrategyInfo(
                    strategy_id=strategy_id,
                    strategy_class=strategy_class,
                    config=config,
                    last_update=datetime.now()
                )
                
                # 如果策略已存在，先停止旧实例
                if strategy_id in self._strategies:
                    old_info = self._strategies[strategy_id]
                    if old_info.instance:
                        await old_info.instance.stop()
                        await old_info.instance.cleanup()
                
                # 创建策略实例
                strategy_instance = strategy_class(strategy_id, config)
                
                # 创建策略上下文
                context = self._context_manager.create_context(strategy_id, config)
                
                # 初始化策略
                await strategy_instance.initialize(context)
                
                strategy_info.instance = strategy_instance
                self._strategies[strategy_id] = strategy_info
                
                self.logger.info(f"策略已注册: {strategy_id}")
        
        except Exception as e:
            self.logger.error(f"注册策略失败 {strategy_id}: {e}")
            raise StrategyLoadException(f"注册策略失败: {e}", strategy_name=strategy_id)
    
    async def unregister_strategy(self, strategy_id: str) -> bool:
        """注销策略"""
        try:
            with self._lock:
                if strategy_id not in self._strategies:
                    return False
                
                strategy_info = self._strategies[strategy_id]
                
                # 停止策略实例
                if strategy_info.instance:
                    await strategy_info.instance.stop()
                    await strategy_info.instance.cleanup()
                
                # 移除上下文
                self._context_manager.remove_context(strategy_id)
                
                # 取消调度
                self._scheduler.unschedule_strategy(strategy_id)
                
                # 移除策略
                del self._strategies[strategy_id]
                
                self.logger.info(f"策略已注销: {strategy_id}")
                return True
        
        except Exception as e:
            self.logger.error(f"注销策略失败 {strategy_id}: {e}")
            return False
    
    async def execute_strategies(self, market_data: MarketData) -> None:
        """
        执行所有活跃策略
        
        Args:
            market_data: 市场数据
        """
        if not self._running:
            return
        
        # 并发执行策略（如果支持自由线程）
        tasks = []
        
        with self._lock:
            active_strategies = [
                (strategy_id, info) for strategy_id, info in self._strategies.items()
                if info.instance and info.instance.is_active() and info.enabled
            ]
        
        for strategy_id, strategy_info in active_strategies:
            task = asyncio.create_task(
                self._execute_single_strategy(strategy_id, strategy_info, market_data)
            )
            tasks.append(task)
        
        if tasks:
            # 等待所有策略执行完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理执行结果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    strategy_id = active_strategies[i][0]
                    self.logger.error(f"策略执行异常 {strategy_id}: {result}")
    
    @PerformanceUtils.async_timing_decorator
    async def _execute_single_strategy(
        self, 
        strategy_id: str, 
        strategy_info: StrategyInfo,
        market_data: MarketData
    ) -> None:
        """执行单个策略"""
        try:
            strategy = strategy_info.instance
            if not strategy:
                return
            
            # 更新上下文数据
            self._context_manager.update_context_data(strategy_id, market_data)
            
            # 执行策略
            start_time = datetime.now()
            signals = await strategy.on_data(market_data)
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 记录执行时间
            if strategy_id not in self._execution_times:
                self._execution_times[strategy_id] = []
            
            self._execution_times[strategy_id].append(execution_time)
            if len(self._execution_times[strategy_id]) > 100:
                self._execution_times[strategy_id] = self._execution_times[strategy_id][-100:]
            
            # 更新策略指标
            strategy.metrics.avg_execution_time = sum(self._execution_times[strategy_id]) / len(self._execution_times[strategy_id])
            
            # 发布信号
            for signal in signals:
                await self.signal_bus.publish_signal(signal)
                strategy.metrics.successful_signals += 1
            
        except Exception as e:
            strategy.metrics.failed_signals += 1
            strategy.metrics.error_count += 1
            strategy.metrics.last_error = str(e)
            
            self.logger.error(f"策略执行失败 {strategy_id}: {e}")
            raise StrategyExecutionException(f"策略执行失败: {e}", strategy_name=strategy_id)
    
    async def _load_configured_strategies(self) -> None:
        """加载配置的策略"""
        try:
            strategies_config = self.config.get_config("strategies.default", {})
            
            for strategy_name, strategy_config in strategies_config.items():
                if strategy_config.get("enabled", False):
                    if strategy_name in STRATEGY_REGISTRY:
                        strategy_class = STRATEGY_REGISTRY[strategy_name]
                        await self.register_strategy(strategy_name, strategy_class, strategy_config)
                    else:
                        self.logger.warning(f"未知的策略类型: {strategy_name}")
        
        except Exception as e:
            self.logger.error(f"加载配置策略失败: {e}")
    
    def get_strategy_info(self, strategy_id: str) -> Optional[StrategyInfo]:
        """获取策略信息"""
        with self._lock:
            return self._strategies.get(strategy_id)
    
    def get_all_strategies(self) -> Dict[str, StrategyInfo]:
        """获取所有策略信息"""
        with self._lock:
            return self._strategies.copy()
    
    def get_strategy_metrics(self, strategy_id: str) -> Optional[StrategyMetrics]:
        """获取策略指标"""
        strategy_info = self.get_strategy_info(strategy_id)
        if strategy_info and strategy_info.instance:
            return strategy_info.instance.get_metrics()
        return None
