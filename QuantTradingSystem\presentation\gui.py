"""
GUI模块

图形用户界面 - 提供直观的用户交互界面。
"""

import asyncio
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
import threading
import queue
import json

from shared.utils import Logger
from shared.constants import TimeFrame, StrategyStatus
from infrastructure.config import ConfigurationManager
from presentation.controllers import MainController


class AsyncTkinter:
    """异步Tkinter包装器"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.loop = asyncio.new_event_loop()
        self.thread = threading.Thread(target=self._run_loop, daemon=True)
        self.thread.start()
    
    def _run_loop(self):
        """运行异步事件循环"""
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()
    
    def run_async(self, coro):
        """在异步循环中运行协程"""
        future = asyncio.run_coroutine_threadsafe(coro, self.loop)
        return future
    
    def stop(self):
        """停止异步循环"""
        self.loop.call_soon_threadsafe(self.loop.stop)


class StatusBar(ttk.Frame):
    """状态栏组件"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        # 状态标签
        self.status_label = ttk.Label(self, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(self, length=200, mode='determinate')
        self.progress.pack(side=tk.RIGHT, padx=5)
        
        # 时间标签
        self.time_label = ttk.Label(self, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)
        
        # 更新时间
        self._update_time()
    
    def set_status(self, text: str):
        """设置状态文本"""
        self.status_label.config(text=text)
    
    def set_progress(self, value: float):
        """设置进度值 (0-100)"""
        self.progress['value'] = value
    
    def _update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.after(1000, self._update_time)


class StrategyPanel(ttk.LabelFrame):
    """策略面板"""
    
    def __init__(self, parent, controller: MainController):
        super().__init__(parent, text="策略管理", padding=10)
        self.controller = controller
        
        self._create_widgets()
        self._setup_layout()
    
    def _create_widgets(self):
        """创建组件"""
        # 策略列表
        self.strategy_tree = ttk.Treeview(self, columns=('status', 'config'), show='tree headings')
        self.strategy_tree.heading('#0', text='策略名称')
        self.strategy_tree.heading('status', text='状态')
        self.strategy_tree.heading('config', text='配置')
        
        # 滚动条
        self.strategy_scrollbar = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.strategy_tree.yview)
        self.strategy_tree.configure(yscrollcommand=self.strategy_scrollbar.set)
        
        # 按钮框架
        self.button_frame = ttk.Frame(self)
        
        # 按钮
        self.load_button = ttk.Button(self.button_frame, text="加载策略", command=self._load_strategy)
        self.start_button = ttk.Button(self.button_frame, text="启动策略", command=self._start_strategy)
        self.stop_button = ttk.Button(self.button_frame, text="停止策略", command=self._stop_strategy)
        self.config_button = ttk.Button(self.button_frame, text="配置策略", command=self._config_strategy)
    
    def _setup_layout(self):
        """设置布局"""
        # 策略列表
        self.strategy_tree.grid(row=0, column=0, sticky='nsew', padx=(0, 5))
        self.strategy_scrollbar.grid(row=0, column=1, sticky='ns')
        
        # 按钮
        self.button_frame.grid(row=1, column=0, columnspan=2, sticky='ew', pady=(10, 0))
        self.load_button.pack(side=tk.LEFT, padx=(0, 5))
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        self.stop_button.pack(side=tk.LEFT, padx=(0, 5))
        self.config_button.pack(side=tk.LEFT)
        
        # 配置权重
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
    
    def _load_strategy(self):
        """加载策略"""
        # 策略选择对话框
        strategies = ['sma_cross', 'rsi', 'macd']  # 可用策略列表
        
        dialog = StrategySelectionDialog(self, strategies)
        if dialog.result:
            strategy_name = dialog.result
            # 异步加载策略
            future = self.controller.async_tk.run_async(
                self.controller.load_strategy(strategy_name)
            )
            self._handle_async_result(future, f"策略 {strategy_name} 加载")
    
    def _start_strategy(self):
        """启动策略"""
        selected = self.strategy_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要启动的策略")
            return
        
        strategy_name = self.strategy_tree.item(selected[0])['text']
        future = self.controller.async_tk.run_async(
            self.controller.start_strategy(strategy_name)
        )
        self._handle_async_result(future, f"策略 {strategy_name} 启动")
    
    def _stop_strategy(self):
        """停止策略"""
        selected = self.strategy_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要停止的策略")
            return
        
        strategy_name = self.strategy_tree.item(selected[0])['text']
        future = self.controller.async_tk.run_async(
            self.controller.stop_strategy(strategy_name)
        )
        self._handle_async_result(future, f"策略 {strategy_name} 停止")
    
    def _config_strategy(self):
        """配置策略"""
        selected = self.strategy_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要配置的策略")
            return
        
        strategy_name = self.strategy_tree.item(selected[0])['text']
        # 打开配置对话框
        dialog = StrategyConfigDialog(self, strategy_name, self.controller)
    
    def _handle_async_result(self, future, operation: str):
        """处理异步操作结果"""
        def check_result():
            try:
                if future.done():
                    result = future.result()
                    if result:
                        messagebox.showinfo("成功", f"{operation}成功")
                        self.refresh_strategies()
                    else:
                        messagebox.showerror("失败", f"{operation}失败")
                else:
                    self.after(100, check_result)
            except Exception as e:
                messagebox.showerror("错误", f"{operation}出错: {str(e)}")
        
        check_result()
    
    def refresh_strategies(self):
        """刷新策略列表"""
        # 清空现有项目
        for item in self.strategy_tree.get_children():
            self.strategy_tree.delete(item)
        
        # 获取策略信息
        strategies = self.controller.get_strategies()
        for strategy_name, strategy_info in strategies.items():
            status = strategy_info.get('status', 'UNKNOWN')
            config = str(strategy_info.get('config', {}))
            
            self.strategy_tree.insert('', 'end', text=strategy_name, 
                                    values=(status, config))


class BacktestPanel(ttk.LabelFrame):
    """回测面板"""
    
    def __init__(self, parent, controller: MainController):
        super().__init__(parent, text="回测管理", padding=10)
        self.controller = controller
        
        self._create_widgets()
        self._setup_layout()
    
    def _create_widgets(self):
        """创建组件"""
        # 参数框架
        self.params_frame = ttk.Frame(self)
        
        # 策略选择
        ttk.Label(self.params_frame, text="策略:").grid(row=0, column=0, sticky='w', padx=(0, 5))
        self.strategy_var = tk.StringVar()
        self.strategy_combo = ttk.Combobox(self.params_frame, textvariable=self.strategy_var,
                                         values=['sma_cross', 'rsi', 'macd'])
        self.strategy_combo.grid(row=0, column=1, sticky='ew', padx=(0, 10))
        
        # 品种选择
        ttk.Label(self.params_frame, text="品种:").grid(row=0, column=2, sticky='w', padx=(0, 5))
        self.symbol_var = tk.StringVar(value="AAPL")
        self.symbol_entry = ttk.Entry(self.params_frame, textvariable=self.symbol_var)
        self.symbol_entry.grid(row=0, column=3, sticky='ew')
        
        # 日期选择
        ttk.Label(self.params_frame, text="开始日期:").grid(row=1, column=0, sticky='w', padx=(0, 5), pady=(5, 0))
        self.start_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d"))
        self.start_date_entry = ttk.Entry(self.params_frame, textvariable=self.start_date_var)
        self.start_date_entry.grid(row=1, column=1, sticky='ew', padx=(0, 10), pady=(5, 0))
        
        ttk.Label(self.params_frame, text="结束日期:").grid(row=1, column=2, sticky='w', padx=(0, 5), pady=(5, 0))
        self.end_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.end_date_entry = ttk.Entry(self.params_frame, textvariable=self.end_date_var)
        self.end_date_entry.grid(row=1, column=3, sticky='ew', pady=(5, 0))
        
        # 初始资金
        ttk.Label(self.params_frame, text="初始资金:").grid(row=2, column=0, sticky='w', padx=(0, 5), pady=(5, 0))
        self.capital_var = tk.StringVar(value="100000")
        self.capital_entry = ttk.Entry(self.params_frame, textvariable=self.capital_var)
        self.capital_entry.grid(row=2, column=1, sticky='ew', padx=(0, 10), pady=(5, 0))
        
        # 按钮
        self.run_button = ttk.Button(self.params_frame, text="运行回测", command=self._run_backtest)
        self.run_button.grid(row=2, column=2, columnspan=2, sticky='ew', pady=(5, 0))
        
        # 结果显示
        self.result_text = tk.Text(self, height=15, wrap=tk.WORD)
        self.result_scrollbar = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=self.result_scrollbar.set)
    
    def _setup_layout(self):
        """设置布局"""
        # 参数框架
        self.params_frame.grid(row=0, column=0, columnspan=2, sticky='ew', pady=(0, 10))
        self.params_frame.grid_columnconfigure(1, weight=1)
        self.params_frame.grid_columnconfigure(3, weight=1)
        
        # 结果显示
        self.result_text.grid(row=1, column=0, sticky='nsew', padx=(0, 5))
        self.result_scrollbar.grid(row=1, column=1, sticky='ns')
        
        # 配置权重
        self.grid_rowconfigure(1, weight=1)
        self.grid_columnconfigure(0, weight=1)
    
    def _run_backtest(self):
        """运行回测"""
        try:
            # 获取参数
            strategy_name = self.strategy_var.get()
            symbol = self.symbol_var.get()
            start_date = datetime.strptime(self.start_date_var.get(), "%Y-%m-%d")
            end_date = datetime.strptime(self.end_date_var.get(), "%Y-%m-%d")
            initial_capital = float(self.capital_var.get())
            
            if not strategy_name:
                messagebox.showwarning("警告", "请选择策略")
                return
            
            # 清空结果显示
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "回测运行中...\n")
            
            # 运行回测
            future = self.controller.async_tk.run_async(
                self.controller.run_backtest(
                    strategy_name, symbol, start_date, end_date, initial_capital,
                    self._update_progress
                )
            )
            
            self._handle_backtest_result(future)
            
        except ValueError as e:
            messagebox.showerror("错误", f"参数错误: {str(e)}")
        except Exception as e:
            messagebox.showerror("错误", f"回测失败: {str(e)}")
    
    def _update_progress(self, progress: float):
        """更新进度"""
        # 在主线程中更新进度
        self.after_idle(lambda: self.controller.main_window.status_bar.set_progress(progress * 100))
    
    def _handle_backtest_result(self, future):
        """处理回测结果"""
        def check_result():
            try:
                if future.done():
                    result = future.result()
                    self._display_backtest_result(result)
                    self.controller.main_window.status_bar.set_progress(0)
                else:
                    self.after(100, check_result)
            except Exception as e:
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, f"回测失败: {str(e)}\n")
                self.controller.main_window.status_bar.set_progress(0)
        
        check_result()
    
    def _display_backtest_result(self, result):
        """显示回测结果"""
        self.result_text.delete(1.0, tk.END)
        
        if result.error_message:
            self.result_text.insert(tk.END, f"回测失败: {result.error_message}\n")
            return
        
        # 显示性能指标
        performance = result.performance
        self.result_text.insert(tk.END, "=== 回测结果 ===\n")
        self.result_text.insert(tk.END, f"总收益率: {performance.total_return:.2%}\n")
        self.result_text.insert(tk.END, f"年化收益率: {performance.annualized_return:.2%}\n")
        self.result_text.insert(tk.END, f"夏普比率: {performance.sharpe_ratio:.3f}\n")
        self.result_text.insert(tk.END, f"最大回撤: {performance.max_drawdown:.2%}\n")
        self.result_text.insert(tk.END, f"胜率: {performance.win_rate:.2%}\n")
        self.result_text.insert(tk.END, f"盈亏比: {performance.profit_factor:.3f}\n")
        self.result_text.insert(tk.END, f"\n交易次数: {len(result.trade_history)}\n")
        self.result_text.insert(tk.END, f"信号次数: {len(result.signal_history)}\n")


class StrategySelectionDialog:
    """策略选择对话框"""
    
    def __init__(self, parent, strategies: List[str]):
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("选择策略")
        self.dialog.geometry("300x200")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 策略列表
        ttk.Label(self.dialog, text="请选择要加载的策略:").pack(pady=10)
        
        self.strategy_var = tk.StringVar()
        for strategy in strategies:
            ttk.Radiobutton(self.dialog, text=strategy, variable=self.strategy_var, 
                          value=strategy).pack(anchor='w', padx=20)
        
        # 按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="确定", command=self._ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self._cancel).pack(side=tk.LEFT, padx=5)
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
        # 等待结果
        self.dialog.wait_window()
    
    def _ok(self):
        """确定按钮"""
        self.result = self.strategy_var.get()
        self.dialog.destroy()
    
    def _cancel(self):
        """取消按钮"""
        self.dialog.destroy()


class StrategyConfigDialog:
    """策略配置对话框"""
    
    def __init__(self, parent, strategy_name: str, controller: MainController):
        self.strategy_name = strategy_name
        self.controller = controller
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"配置策略 - {strategy_name}")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 获取当前配置
        self.config = controller.get_strategy_config(strategy_name)
        
        self._create_widgets()
        self._setup_layout()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def _create_widgets(self):
        """创建组件"""
        # 配置编辑器
        ttk.Label(self.dialog, text="策略配置 (JSON格式):").pack(pady=(10, 5))
        
        self.config_text = tk.Text(self.dialog, height=15, wrap=tk.WORD)
        self.config_scrollbar = ttk.Scrollbar(self.dialog, orient=tk.VERTICAL, 
                                            command=self.config_text.yview)
        self.config_text.configure(yscrollcommand=self.config_scrollbar.set)
        
        # 显示当前配置
        config_json = json.dumps(self.config, indent=2, ensure_ascii=False)
        self.config_text.insert(tk.END, config_json)
        
        # 按钮
        self.button_frame = ttk.Frame(self.dialog)
        self.save_button = ttk.Button(self.button_frame, text="保存", command=self._save)
        self.cancel_button = ttk.Button(self.button_frame, text="取消", command=self._cancel)
    
    def _setup_layout(self):
        """设置布局"""
        # 配置编辑器
        config_frame = ttk.Frame(self.dialog)
        config_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        self.config_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.config_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮
        self.button_frame.pack(pady=10)
        self.save_button.pack(side=tk.LEFT, padx=5)
        self.cancel_button.pack(side=tk.LEFT, padx=5)
    
    def _save(self):
        """保存配置"""
        try:
            # 解析JSON配置
            config_text = self.config_text.get(1.0, tk.END)
            new_config = json.loads(config_text)
            
            # 更新配置
            future = self.controller.async_tk.run_async(
                self.controller.update_strategy_config(self.strategy_name, new_config)
            )
            
            # 等待结果
            def check_result():
                if future.done():
                    try:
                        result = future.result()
                        if result:
                            messagebox.showinfo("成功", "配置已保存")
                            self.dialog.destroy()
                        else:
                            messagebox.showerror("失败", "配置保存失败")
                    except Exception as e:
                        messagebox.showerror("错误", f"配置保存出错: {str(e)}")
                else:
                    self.dialog.after(100, check_result)
            
            check_result()
            
        except json.JSONDecodeError as e:
            messagebox.showerror("错误", f"JSON格式错误: {str(e)}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def _cancel(self):
        """取消"""
        self.dialog.destroy()
