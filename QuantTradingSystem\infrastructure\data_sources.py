"""
数据源模块

实现各种数据源的统一接口，支持多种数据提供商和数据格式。
利用Python 3.13的异步特性和并发能力提供高性能的数据获取。
"""

import asyncio
import aiohttp
import json
import pandas as pd
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional, Callable, AsyncGenerator
from pathlib import Path
import websockets
import time

from domain.value_objects import MarketData, Price, Volume
from shared.constants import TimeFrame, AssetType
from shared.exceptions import (
    DataSourceException, DataConnectionException, 
    DataValidationException, DataRateLimitException
)
from shared.utils import Logger, AsyncUtils, CacheUtils
from .config import ConfigurationManager


class IDataSource(ABC):
    """
    数据源统一接口
    
    信息隐藏的关键 - 将复杂的数据获取和处理逻辑隐藏在统一的抽象接口之后。
    """
    
    @abstractmethod
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame,
        start_date: datetime, 
        end_date: datetime
    ) -> List[MarketData]:
        """获取历史数据"""
        pass
    
    @abstractmethod
    async def get_realtime_data(self, symbol: str) -> MarketData:
        """获取实时数据"""
        pass
    
    @abstractmethod
    async def subscribe_realtime_feed(
        self, 
        symbols: List[str], 
        callback: Callable[[MarketData], None]
    ) -> None:
        """订阅实时数据流"""
        pass
    
    @abstractmethod
    async def get_available_symbols(self) -> List[str]:
        """获取可用的交易品种"""
        pass
    
    @abstractmethod
    async def validate_symbol(self, symbol: str) -> bool:
        """验证交易品种是否有效"""
        pass
    
    @abstractmethod
    async def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取交易品种信息"""
        pass


class BaseDataSource(IDataSource):
    """数据源基类"""
    
    def __init__(self, config: ConfigurationManager, name: str):
        self.config = config
        self.name = name
        self.logger = Logger.get_logger(f"datasource.{name}")
        self._session: Optional[aiohttp.ClientSession] = None
        self._rate_limiter = RateLimiter()
        self._cache = {}
    
    async def __aenter__(self):
        await self._initialize_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self._cleanup_session()
    
    async def _initialize_session(self) -> None:
        """初始化HTTP会话"""
        if self._session is None:
            timeout = aiohttp.ClientTimeout(total=30)
            self._session = aiohttp.ClientSession(timeout=timeout)
    
    async def _cleanup_session(self) -> None:
        """清理HTTP会话"""
        if self._session:
            await self._session.close()
            self._session = None
    
    async def _make_request(
        self, 
        url: str, 
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """发起HTTP请求"""
        if not self._session:
            await self._initialize_session()
        
        # 应用速率限制
        await self._rate_limiter.acquire()
        
        try:
            async with self._session.get(url, params=params, headers=headers) as response:
                if response.status == 429:  # Too Many Requests
                    retry_after = int(response.headers.get('Retry-After', 60))
                    raise DataRateLimitException(
                        f"API速率限制，请在{retry_after}秒后重试",
                        source_name=self.name,
                        retry_after=retry_after
                    )
                
                response.raise_for_status()
                return await response.json()
                
        except aiohttp.ClientError as e:
            raise DataConnectionException(
                f"网络请求失败: {e}",
                source_name=self.name
            )
        except json.JSONDecodeError as e:
            raise DataSourceException(
                f"JSON解析失败: {e}",
                source_name=self.name
            )
    
    def _validate_market_data(self, data: MarketData) -> bool:
        """验证市场数据"""
        try:
            # 基本验证
            if not data.symbol or not data.timestamp:
                return False
            
            # 价格验证
            if data.high.value < data.low.value:
                return False
            
            if data.high.value < max(data.open.value, data.close.value):
                return False
            
            if data.low.value > min(data.open.value, data.close.value):
                return False
            
            # 成交量验证
            if data.volume.value < 0:
                return False
            
            return True
            
        except Exception:
            return False


class BinanceDataSource(BaseDataSource):
    """Binance数据源"""
    
    def __init__(self, config: ConfigurationManager):
        super().__init__(config, "binance")
        
        # 获取配置
        self.api_key = config.get_config("data_sources.binance.api_key", "")
        self.secret_key = config.get_config("data_sources.binance.secret_key", "")
        self.use_testnet = config.get_config("data_sources.binance.use_testnet", True)
        
        if self.use_testnet:
            self.base_url = "https://testnet.binance.vision/api/v3"
            self.ws_url = "wss://testnet.binance.vision/ws"
        else:
            self.base_url = "https://api.binance.com/api/v3"
            self.ws_url = "wss://stream.binance.com:9443/ws"
        
        # 设置速率限制
        rate_limit = config.get_config("data_sources.binance.rate_limit", 1200)
        self._rate_limiter = RateLimiter(rate_limit, 60)  # 每分钟限制
    
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame,
        start_date: datetime, 
        end_date: datetime
    ) -> List[MarketData]:
        """获取历史K线数据"""
        try:
            # 转换时间格式
            start_time = int(start_date.timestamp() * 1000)
            end_time = int(end_date.timestamp() * 1000)
            
            # 转换时间周期
            interval = self._convert_timeframe(timeframe)
            
            # 构建请求参数
            params = {
                'symbol': symbol.upper(),
                'interval': interval,
                'startTime': start_time,
                'endTime': end_time,
                'limit': 1000
            }
            
            # 发起请求
            url = f"{self.base_url}/klines"
            response = await self._make_request(url, params)
            
            # 解析数据
            market_data_list = []
            for kline in response:
                market_data = self._parse_kline_data(symbol, kline, timeframe)
                if self._validate_market_data(market_data):
                    market_data_list.append(market_data)
            
            self.logger.info(f"获取历史数据成功: {symbol}, {len(market_data_list)}条记录")
            return market_data_list
            
        except Exception as e:
            self.logger.error(f"获取历史数据失败: {symbol}, {e}")
            raise DataSourceException(f"获取历史数据失败: {e}", source_name=self.name)
    
    async def get_realtime_data(self, symbol: str) -> MarketData:
        """获取实时价格数据"""
        try:
            params = {'symbol': symbol.upper()}
            url = f"{self.base_url}/ticker/24hr"
            response = await self._make_request(url, params)
            
            # 解析实时数据
            market_data = self._parse_ticker_data(symbol, response)
            
            if not self._validate_market_data(market_data):
                raise DataValidationException(f"实时数据验证失败: {symbol}")
            
            return market_data
            
        except Exception as e:
            self.logger.error(f"获取实时数据失败: {symbol}, {e}")
            raise DataSourceException(f"获取实时数据失败: {e}", source_name=self.name)
    
    async def subscribe_realtime_feed(
        self, 
        symbols: List[str], 
        callback: Callable[[MarketData], None]
    ) -> None:
        """订阅实时数据流"""
        max_retries = 5
        retry_delay = 5
        
        for attempt in range(max_retries):
            try:
                # 构建WebSocket流名称 - 使用正确的Binance WebSocket格式
                if len(symbols) == 1:
                    # 单个流
                    stream_name = f"{symbols[0].lower()}@ticker"
                    stream_url = f"{self.ws_url}/{stream_name}"
                else:
                    # 多个流 - 使用combined stream
                    streams = [f"{symbol.lower()}@ticker" for symbol in symbols]
                    stream_url = f"{self.ws_url}/stream?streams={'/'.join(streams)}"
                
                self.logger.info(f"尝试连接WebSocket: {stream_url}")
                
                # 设置连接参数
                connect_kwargs = {
                    'ping_interval': 20,
                    'ping_timeout': 10,
                    'close_timeout': 10
                }
                
                async with websockets.connect(stream_url, **connect_kwargs) as websocket:
                    self.logger.info(f"WebSocket连接成功: {symbols}")
                    
                    async for message in websocket:
                        try:
                            data = json.loads(message)
                            
                            # 处理单流和多流的不同格式
                            if len(symbols) == 1:
                                # 单流格式：直接是ticker数据
                                if 's' in data:  # 确保是ticker数据
                                    symbol = data['s']
                                    market_data = self._parse_websocket_data(symbol, data)
                                    
                                    if self._validate_market_data(market_data):
                                        callback(market_data)
                            else:
                                # 多流格式：包含stream和data字段
                                if 'stream' in data and 'data' in data:
                                    symbol = data['data']['s']
                                    market_data = self._parse_websocket_data(symbol, data['data'])
                                    
                                    if self._validate_market_data(market_data):
                                        callback(market_data)
                            
                        except json.JSONDecodeError as e:
                            self.logger.error(f"JSON解析失败: {e}")
                        except Exception as e:
                            self.logger.error(f"WebSocket数据解析失败: {e}")
                            
            except websockets.exceptions.ConnectionClosed as e:
                self.logger.warning(f"WebSocket连接关闭: {e}")
                if attempt < max_retries - 1:
                    self.logger.info(f"等待{retry_delay}秒后重连...")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    raise DataConnectionException(f"WebSocket连接失败，已重试{max_retries}次", source_name=self.name)
            except Exception as e:
                self.logger.error(f"WebSocket连接失败: {e}")
                if attempt < max_retries - 1:
                    self.logger.info(f"等待{retry_delay}秒后重连...")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    raise DataConnectionException(f"WebSocket连接失败: {e}", source_name=self.name)
    
    async def get_available_symbols(self) -> List[str]:
        """获取可用的交易品种"""
        try:
            url = f"{self.base_url}/exchangeInfo"
            response = await self._make_request(url)
            
            symbols = []
            for symbol_info in response.get('symbols', []):
                if symbol_info.get('status') == 'TRADING':
                    symbols.append(symbol_info['symbol'])
            
            return symbols
            
        except Exception as e:
            self.logger.error(f"获取交易品种失败: {e}")
            raise DataSourceException(f"获取交易品种失败: {e}", source_name=self.name)
    
    async def validate_symbol(self, symbol: str) -> bool:
        """验证交易品种是否有效"""
        try:
            available_symbols = await self.get_available_symbols()
            return symbol.upper() in available_symbols
        except Exception:
            return False
    
    async def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取交易品种信息"""
        try:
            url = f"{self.base_url}/exchangeInfo"
            params = {'symbol': symbol.upper()}
            response = await self._make_request(url, params)
            
            for symbol_info in response.get('symbols', []):
                if symbol_info['symbol'] == symbol.upper():
                    return symbol_info
            
            raise DataSourceException(f"交易品种不存在: {symbol}")
            
        except Exception as e:
            self.logger.error(f"获取交易品种信息失败: {symbol}, {e}")
            raise DataSourceException(f"获取交易品种信息失败: {e}", source_name=self.name)
    
    def _convert_timeframe(self, timeframe: TimeFrame) -> str:
        """转换时间周期格式"""
        mapping = {
            TimeFrame.M1: '1m',
            TimeFrame.M3: '3m',
            TimeFrame.M5: '5m',
            TimeFrame.M15: '15m',
            TimeFrame.M30: '30m',
            TimeFrame.H1: '1h',
            TimeFrame.H2: '2h',
            TimeFrame.H4: '4h',
            TimeFrame.H6: '6h',
            TimeFrame.H8: '8h',
            TimeFrame.H12: '12h',
            TimeFrame.D1: '1d',
            TimeFrame.D3: '3d',
            TimeFrame.W1: '1w',
            TimeFrame.MON1: '1M'
        }
        return mapping.get(timeframe, '1d')
    
    def _parse_kline_data(self, symbol: str, kline: List, timeframe: TimeFrame) -> MarketData:
        """解析K线数据"""
        return MarketData(
            symbol=symbol,
            timestamp=datetime.fromtimestamp(kline[0] / 1000),
            open=Price(Decimal(kline[1])),
            high=Price(Decimal(kline[2])),
            low=Price(Decimal(kline[3])),
            close=Price(Decimal(kline[4])),
            volume=Volume(Decimal(kline[5])),
            timeframe=timeframe,
            asset_type=AssetType.CRYPTO
        )
    
    def _parse_ticker_data(self, symbol: str, ticker: Dict[str, Any]) -> MarketData:
        """解析24小时价格数据"""
        return MarketData(
            symbol=symbol,
            timestamp=datetime.now(),
            open=Price(Decimal(ticker['openPrice'])),
            high=Price(Decimal(ticker['highPrice'])),
            low=Price(Decimal(ticker['lowPrice'])),
            close=Price(Decimal(ticker['lastPrice'])),
            volume=Volume(Decimal(ticker['volume'])),
            timeframe=TimeFrame.D1,
            asset_type=AssetType.CRYPTO
        )
    
    def _parse_websocket_data(self, symbol: str, data: Dict[str, Any]) -> MarketData:
        """解析WebSocket数据"""
        return MarketData(
            symbol=symbol,
            timestamp=datetime.fromtimestamp(data['E'] / 1000),
            open=Price(Decimal(data['o'])),
            high=Price(Decimal(data['h'])),
            low=Price(Decimal(data['l'])),
            close=Price(Decimal(data['c'])),
            volume=Volume(Decimal(data['v'])),
            timeframe=TimeFrame.D1,
            asset_type=AssetType.CRYPTO
        )


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int = 100, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """获取请求许可"""
        async with self._lock:
            now = time.time()
            
            # 清理过期的请求记录
            self.requests = [req_time for req_time in self.requests 
                           if now - req_time < self.time_window]
            
            # 检查是否超过限制
            if len(self.requests) >= self.max_requests:
                # 计算需要等待的时间
                oldest_request = min(self.requests)
                wait_time = self.time_window - (now - oldest_request)
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
            
            # 记录当前请求
            self.requests.append(now)


class YahooFinanceDataSource(BaseDataSource):
    """Yahoo Finance数据源"""

    def __init__(self, config: ConfigurationManager):
        super().__init__(config, "yahoo_finance")
        self.base_url = "https://query1.finance.yahoo.com/v8/finance/chart"

        # 设置速率限制
        rate_limit = config.get_config("data_sources.yahoo_finance.rate_limit", 100)
        self._rate_limiter = RateLimiter(rate_limit, 60)

    async def get_historical_data(
        self,
        symbol: str,
        timeframe: TimeFrame,
        start_date: datetime,
        end_date: datetime
    ) -> List[MarketData]:
        """获取历史数据"""
        try:
            # 转换参数
            period1 = int(start_date.timestamp())
            period2 = int(end_date.timestamp())
            interval = self._convert_timeframe(timeframe)

            # 构建URL
            url = f"{self.base_url}/{symbol}"
            params = {
                'period1': period1,
                'period2': period2,
                'interval': interval,
                'includePrePost': 'false'
            }

            # 发起请求
            response = await self._make_request(url, params)

            # 解析数据
            return self._parse_yahoo_data(symbol, response, timeframe)

        except Exception as e:
            self.logger.error(f"获取Yahoo Finance历史数据失败: {symbol}, {e}")
            raise DataSourceException(f"获取历史数据失败: {e}", source_name=self.name)

    async def get_realtime_data(self, symbol: str) -> MarketData:
        """获取实时数据"""
        # Yahoo Finance的实时数据通过历史数据接口获取最新数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=1)

        data_list = await self.get_historical_data(symbol, TimeFrame.D1, start_date, end_date)
        if data_list:
            return data_list[-1]
        else:
            raise DataSourceException(f"无法获取实时数据: {symbol}", source_name=self.name)

    async def subscribe_realtime_feed(
        self,
        symbols: List[str],
        callback: Callable[[MarketData], None]
    ) -> None:
        """Yahoo Finance不支持WebSocket，使用轮询方式"""
        self.logger.warning("Yahoo Finance不支持实时数据流，使用轮询方式")

        while True:
            try:
                for symbol in symbols:
                    data = await self.get_realtime_data(symbol)
                    callback(data)

                # 等待30秒后再次轮询
                await asyncio.sleep(30)

            except Exception as e:
                self.logger.error(f"轮询数据失败: {e}")
                await asyncio.sleep(60)  # 出错时等待更长时间

    async def get_available_symbols(self) -> List[str]:
        """Yahoo Finance没有直接的API获取所有品种，返回常用品种"""
        return [
            'AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA',
            'SPY', 'QQQ', 'IWM', 'GLD', 'SLV',
            'BTC-USD', 'ETH-USD', 'ADA-USD'
        ]

    async def validate_symbol(self, symbol: str) -> bool:
        """验证品种是否有效"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            data = await self.get_historical_data(symbol, TimeFrame.D1, start_date, end_date)
            return len(data) > 0
        except Exception:
            return False

    async def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取品种信息"""
        return {
            'symbol': symbol,
            'source': 'yahoo_finance',
            'asset_type': 'stock'  # 简化处理
        }

    def _convert_timeframe(self, timeframe: TimeFrame) -> str:
        """转换时间周期"""
        mapping = {
            TimeFrame.M1: '1m',
            TimeFrame.M5: '5m',
            TimeFrame.M15: '15m',
            TimeFrame.M30: '30m',
            TimeFrame.H1: '1h',
            TimeFrame.D1: '1d',
            TimeFrame.W1: '1wk',
            TimeFrame.MON1: '1mo'
        }
        return mapping.get(timeframe, '1d')

    def _parse_yahoo_data(self, symbol: str, response: Dict[str, Any], timeframe: TimeFrame) -> List[MarketData]:
        """解析Yahoo Finance数据"""
        result = response.get('chart', {}).get('result', [])
        if not result:
            return []

        data = result[0]
        timestamps = data.get('timestamp', [])
        indicators = data.get('indicators', {}).get('quote', [])

        if not indicators:
            return []

        quote = indicators[0]
        opens = quote.get('open', [])
        highs = quote.get('high', [])
        lows = quote.get('low', [])
        closes = quote.get('close', [])
        volumes = quote.get('volume', [])

        market_data_list = []
        for i in range(len(timestamps)):
            try:
                if all(x is not None for x in [opens[i], highs[i], lows[i], closes[i], volumes[i]]):
                    market_data = MarketData(
                        symbol=symbol,
                        timestamp=datetime.fromtimestamp(timestamps[i]),
                        open=Price(Decimal(str(opens[i]))),
                        high=Price(Decimal(str(highs[i]))),
                        low=Price(Decimal(str(lows[i]))),
                        close=Price(Decimal(str(closes[i]))),
                        volume=Volume(Decimal(str(volumes[i]))),
                        timeframe=timeframe,
                        asset_type=AssetType.STOCK
                    )
                    market_data_list.append(market_data)
            except (ValueError, TypeError, IndexError):
                continue

        return market_data_list


class LocalFileDataSource(BaseDataSource):
    """本地文件数据源"""

    def __init__(self, config: ConfigurationManager):
        super().__init__(config, "local_file")
        self.data_directory = Path(config.get_config("data_sources.local_file.data_directory", "data/market_data"))
        self.data_directory.mkdir(parents=True, exist_ok=True)

    async def get_historical_data(
        self,
        symbol: str,
        timeframe: TimeFrame,
        start_date: datetime,
        end_date: datetime
    ) -> List[MarketData]:
        """从本地文件获取历史数据"""
        try:
            file_path = self.data_directory / f"{symbol}_{timeframe.value}.csv"

            if not file_path.exists():
                self.logger.warning(f"数据文件不存在: {file_path}")
                return []

            # 读取CSV文件
            df = pd.read_csv(file_path)
            df['timestamp'] = pd.to_datetime(df['timestamp'])

            # 过滤时间范围
            mask = (df['timestamp'] >= start_date) & (df['timestamp'] <= end_date)
            df = df.loc[mask]

            # 转换为MarketData对象
            market_data_list = []
            for _, row in df.iterrows():
                market_data = MarketData(
                    symbol=symbol,
                    timestamp=row['timestamp'],
                    open=Price(Decimal(str(row['open']))),
                    high=Price(Decimal(str(row['high']))),
                    low=Price(Decimal(str(row['low']))),
                    close=Price(Decimal(str(row['close']))),
                    volume=Volume(Decimal(str(row['volume']))),
                    timeframe=timeframe,
                    asset_type=AssetType.STOCK
                )
                market_data_list.append(market_data)

            return market_data_list

        except Exception as e:
            self.logger.error(f"读取本地文件失败: {symbol}, {e}")
            raise DataSourceException(f"读取本地文件失败: {e}", source_name=self.name)

    async def get_realtime_data(self, symbol: str) -> MarketData:
        """本地文件数据源不支持实时数据"""
        raise DataSourceException("本地文件数据源不支持实时数据", source_name=self.name)

    async def subscribe_realtime_feed(
        self,
        symbols: List[str],
        callback: Callable[[MarketData], None]
    ) -> None:
        """本地文件数据源不支持实时数据流"""
        raise DataSourceException("本地文件数据源不支持实时数据流", source_name=self.name)

    async def get_available_symbols(self) -> List[str]:
        """获取本地文件中的可用品种"""
        symbols = set()
        for file_path in self.data_directory.glob("*.csv"):
            # 从文件名提取品种名称
            name_parts = file_path.stem.split('_')
            if len(name_parts) >= 2:
                symbol = '_'.join(name_parts[:-1])
                symbols.add(symbol)

        return list(symbols)

    async def validate_symbol(self, symbol: str) -> bool:
        """验证品种是否存在"""
        available_symbols = await self.get_available_symbols()
        return symbol in available_symbols

    async def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取品种信息"""
        return {
            'symbol': symbol,
            'source': 'local_file',
            'asset_type': 'unknown'
        }

    async def save_data_to_file(self, symbol: str, timeframe: TimeFrame, data: List[MarketData]) -> None:
        """保存数据到本地文件"""
        try:
            file_path = self.data_directory / f"{symbol}_{timeframe.value}.csv"

            # 转换为DataFrame
            records = []
            for market_data in data:
                records.append({
                    'timestamp': market_data.timestamp,
                    'open': float(market_data.open.value),
                    'high': float(market_data.high.value),
                    'low': float(market_data.low.value),
                    'close': float(market_data.close.value),
                    'volume': float(market_data.volume.value)
                })

            df = pd.DataFrame(records)
            df.to_csv(file_path, index=False)

            self.logger.info(f"数据已保存到文件: {file_path}, {len(data)}条记录")

        except Exception as e:
            self.logger.error(f"保存数据到文件失败: {symbol}, {e}")
            raise DataSourceException(f"保存数据失败: {e}", source_name=self.name)
